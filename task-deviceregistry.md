
**Task: Android App Phase 6 - Add Device Information Storage to Firebase**

**Objective:**
Extend the existing app to capture and store comprehensive device information including FCM token to Firebase. This creates a device registry for better device management and identification.

**Requirements:**
1. **Keep ALL existing functionality** - notification capture, location tracking, Firebase sync, Remote Config, FCM, and logging
2. **Store device information** in Firebase with detailed device specs
3. **Update FCM token** whenever it changes or refreshes
4. **Store on app startup** and when FCM token updates
5. **Enhanced device identification** beyond just the device ID string

**Firebase Structure Update:**
```
firestore-root/
└── devices/
    └── [DEVICE_ID]/  // e.g., "Redmi-Note-10-Pro-Android12"
        ├── device_info/  // NEW: Single document with device details
        │   ├── device_model: string        // Build.MODEL
        │   ├── device_brand: string        // Build.MANUFACTURER  
        │   ├── device_name: string         // Build.DEVICE
        │   ├── software_id: string         // Android ID / Enrollment ID
        │   ├── android_version: string     // Build.VERSION.RELEASE
        │   ├── api_level: int             // Build.VERSION.SDK_INT
        │   ├── fcm_token: string          // Current FCM registration token
        │   ├── app_version: string        // App version name
        │   ├── first_seen: timestamp      // When device was first registered
        │   ├── last_updated: timestamp    // Last time info was updated
        │   └── device_id: string          // Same as parent collection key
        ├── notifications/
        │   └── {existing structure}
        └── locations/
            └── {existing structure}
```

**Device Information Collection:**
Use the provided code snippet plus additional fields:
```kotlin
// Your provided fields:
val deviceModel = Build.MODEL
val deviceBrand = Build.MANUFACTURER  
val deviceName = Build.DEVICE
val softwareId = // Your Android ID logic

// Additional fields to include:
val androidVersion = Build.VERSION.RELEASE
val apiLevel = Build.VERSION.SDK_INT
val appVersion = BuildConfig.VERSION_NAME
```

**When to Store Device Info:**
1. **App first startup** - create initial device_info document
2. **FCM token refresh** - update fcm_token field only
3. **App version updates** - update app_version and last_updated
4. **Daily check** - verify and update if any info changed

**Implementation Requirements:**
1. Device info collection utility
2. Firebase device registration on startup
3. FCM token update mechanism in FirebaseMessagingService
4. Device info comparison for change detection
5. Proper timestamp handling (first_seen vs last_updated)

**Enhanced Logging Format:**
```
[DEVICE-INFO] 2024-01-15 14:00:00 | Device registered | Redmi-Note-10-Pro-Android12
[DEVICE-INFO] 2024-01-15 14:00:01 | FCM Token: fA7B2cD3e... | STORED
[FCM-TOKEN] 2024-01-15 16:30:25 | Token refreshed | New token stored
[DEVICE-INFO] 2024-01-16 14:00:00 | Daily check | No changes detected
```

**Additional Components Needed:**
1. DeviceInfoManager class for collecting device data
2. Device registration service
3. FCM token refresh handler in FirebaseMessagingService
4. Device info change detection
5. Periodic device info verification

**Storage Logic:**
- **First run**: Create complete device_info document with first_seen timestamp
- **FCM token updates**: Update only fcm_token and last_updated fields
- **App updates**: Update app_version and last_updated fields  
- **Daily verification**: Compare current vs stored info, update if changed

**Important Notes:**
- Handle device info collection failures gracefully
- Store device info before starting other services
- Log FCM token clearly for testing purposes
- Use proper timestamp formats consistent with existing data
- Don't break any existing functionality
- Handle cases where device info collection fails

**Deliverables:**
1. Updated Android Studio project with device info storage
2. Device registration on app startup
3. FCM token storage and refresh handling
4. Device info verification and updates
5. Enhanced logging for device registration activities
6. Updated Firebase structure with device_info collection
