# NotifSync Lite - Setup Instructions (Phase 2)

## Overview
NotifSync Lite Phase 2 is a headless Android app that captures ALL device notifications AND device location, logging both to Android Logcat. The app runs completely in the background with no user interface.

### Features
- **Notification Capture**: Logs all device notifications in real-time
- **Location Tracking**: Captures GPS location every 15 minutes
- **Headless Operation**: No UI, runs entirely in background
- **Battery Optimized**: Uses efficient location settings and WorkManager

## Installation & Setup

### 1. Install the App
- Build and install the APK on your Android device (API 31+ required)
- The app will briefly show a toast message and open notification settings, then become headless

### 2. Enable Notification Access (CRITICAL STEP)
The app **MUST** have notification access permission to capture notifications. Follow these steps:

#### Method 1: Automatic (Recommended)
1. Launch the app from your app drawer
2. The app will automatically open the notification access settings
3. Find "Notif Sync Lite" in the list
4. Toggle the switch to **ON**
5. Confirm when prompted about security risks

#### Method 2: Manual
1. Go to **Settings** > **Apps** > **Special access** > **Notification access**
2. Find "Notif Sync Lite" in the list
3. Toggle the switch to **ON**
4. Confirm when prompted about security risks

### 3. Enable Location Permissions (CRITICAL STEP)
The app **MUST** have location permissions to capture location data. Follow these steps:

#### Step 3a: Basic Location Permission
1. Launch the app from your app drawer
2. Grant location permissions when prompted
3. Choose "While using the app" initially

#### Step 3b: Background Location Permission (Android 10+)
1. Go to **Settings** > **Apps** > **Notif Sync Lite** > **Permissions** > **Location**
2. Select **"Allow all the time"** (critical for background location)
3. Confirm when prompted about battery usage

#### Alternative Method for Background Location
1. Go to **Settings** > **Location** > **App location permissions**
2. Find "Notif Sync Lite"
3. Select **"Allow all the time"**

### 4. Verify Setup
1. Open **Android Studio** or use **ADB** to view logs
2. Filter logs by tags: `NOTIFICATION_CAPTURE` and `LOCATION_CAPTURE`
3. Send yourself a test notification (text message, email, etc.)
4. Wait up to 15 minutes for location capture
5. You should see logs in these formats:
   ```
   [NOTIFICATION] 2024-01-15 14:30:25 | WhatsApp | John Doe | New message received
   [LOCATION] 2024-01-15 14:30:30 | Lat: -6.2088, Lng: 106.8456 | Accuracy: 12.5m
   ```

## Viewing Captured Notifications

### Using Android Studio
1. Open Android Studio
2. Go to **View** > **Tool Windows** > **Logcat**
3. Select your device
4. Filter by tag: `NOTIFICATION_CAPTURE`
5. All captured notifications will appear in real-time

### Using ADB Command Line
```bash
# View all notification logs
adb logcat -s NOTIFICATION_CAPTURE

# View all location logs
adb logcat -s LOCATION_CAPTURE

# View both notification and location logs
adb logcat -s NOTIFICATION_CAPTURE -s LOCATION_CAPTURE

# View logs with timestamp
adb logcat -v time -s NOTIFICATION_CAPTURE -s LOCATION_CAPTURE

# Save logs to file
adb logcat -s NOTIFICATION_CAPTURE -s LOCATION_CAPTURE > capture_logs.log
```

## Log Format

### Notification Logs
Each captured notification follows this format:
```
[NOTIFICATION] YYYY-MM-DD HH:MM:SS | App Name | Title | Content
```

### Location Logs
Each captured location follows this format:
```
[LOCATION] YYYY-MM-DD HH:MM:SS | Lat: latitude, Lng: longitude | Accuracy: meters
```

### Example Combined Logs:
```
[NOTIFICATION] 2024-01-15 14:30:25 | WhatsApp | John Doe | Hey, are you free tonight?
[LOCATION] 2024-01-15 14:30:30 | Lat: -6.2088, Lng: 106.8456 | Accuracy: 12.5m
[NOTIFICATION] 2024-01-15 14:31:10 | Gmail | Google | You have 3 new emails
[LOCATION] 2024-01-15 14:45:30 | Lat: -6.2090, Lng: 106.8460 | Accuracy: 8.2m
[NOTIFICATION] 2024-01-15 14:32:05 | Instagram | Instagram | user123 liked your photo
```

## Troubleshooting

### No Notifications Being Captured
1. **Check notification access permission**: Go to Settings > Apps > Special access > Notification access
2. **Verify the service is running**: Look for "Service connected" message in logs
3. **Restart the device**: Sometimes helps refresh the notification listener service
4. **Check app isn't battery optimized**: Go to Settings > Battery > Battery optimization

### No Location Being Captured
1. **Check location permissions**: Ensure "Allow all the time" is selected for location access
2. **Verify location services are enabled**: Go to Settings > Location and ensure it's ON
3. **Check WorkManager logs**: Look for logs with tag `LOCATION_CAPTURE`
4. **Wait for first capture**: Location is captured every 15 minutes, so wait up to 15 minutes
5. **Check GPS signal**: Ensure device has good GPS reception (try outdoors)
6. **Verify foreground service**: Look for persistent notification about location tracking

### App Not Starting After Boot
1. **Check boot receiver**: Look for boot-related logs with tag `NOTIFICATION_BOOT`
2. **Disable battery optimization**: Some devices kill background services aggressively
3. **Check auto-start permissions**: Some manufacturers require explicit auto-start permission
4. **Verify WorkManager**: Check if WorkManager jobs are being scheduled

### Missing Notifications from Specific Apps
1. **Check app notification settings**: Some apps may have notifications disabled
2. **Test with different apps**: Try messaging apps, email apps, social media apps
3. **Check notification importance**: Only notifications with sufficient importance are captured

### Location Accuracy Issues
1. **Enable high accuracy mode**: Go to Settings > Location > Mode > High accuracy
2. **Check Google Play Services**: Ensure Google Play Services is updated
3. **Clear location cache**: Go to Settings > Apps > Google Play Services > Storage > Clear Cache
4. **Test outdoors**: GPS accuracy is better outdoors with clear sky view

### Battery Optimization Issues
1. **Disable battery optimization**: Settings > Battery > Battery optimization > NotifSync > Don't optimize
2. **Add to auto-start whitelist**: Some manufacturers (Xiaomi, Huawei) require this
3. **Check background app limits**: Ensure the app can run in background
4. **Verify foreground service**: The location service should show a persistent notification

## Technical Details

### Permissions Used
- `BIND_NOTIFICATION_LISTENER_SERVICE`: Required to capture notifications
- `RECEIVE_BOOT_COMPLETED`: Enables auto-start after device boot
- `FOREGROUND_SERVICE`: Allows service to run in background
- `ACCESS_FINE_LOCATION`: Required for precise location capture
- `ACCESS_COARSE_LOCATION`: Fallback for basic location capture
- `ACCESS_BACKGROUND_LOCATION`: Required for location capture when app is not active
- `FOREGROUND_SERVICE_LOCATION`: Required for location foreground service (Android 10+)

### Components
- **NotificationCaptureService**: Main service that captures and logs notifications
- **LocationWorker**: WorkManager worker that captures location every 15 minutes
- **LocationService**: Foreground service for background location access
- **BootReceiver**: Handles device boot events for auto-start
- **MainActivity**: Minimal activity that helps with initial setup and permissions
- **NotifSyncApplication**: Application class that initializes WorkManager

### Supported Android Versions
- **Minimum**: Android 12 (API 31)
- **Target**: Android 14 (API 36)
- **Tested**: Android 12, 13, 14

## Privacy & Security
- **No data storage**: Notifications and location data are only logged, never stored permanently
- **No network access**: App doesn't send data anywhere
- **Local only**: All data stays on your device
- **Open source**: Code is available for review
- **Location privacy**: Location data is only logged locally, never transmitted or stored in files

## Uninstalling
To completely remove the app:
1. Go to Settings > Apps > Notif Sync Lite
2. Tap "Uninstall"
3. The notification access permission will be automatically revoked

## Support
If you encounter issues:
1. Check the troubleshooting section above
2. Review the logs for error messages
3. Ensure your device meets the minimum requirements (Android 12+)
