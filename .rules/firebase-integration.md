# NotifSync Lite - Firebase Integration Rules

## Firebase Architecture Overview

### Core Firebase Services
1. **Firebase Firestore**: Cloud data storage for notifications and locations
2. **Firebase Authentication**: Anonymous authentication for device access
3. **Firebase Remote Config**: Dynamic configuration management
4. **Firebase Analytics**: Basic usage analytics (optional)

### Data Structure
```
firestore-root/
└── devices/
    └── [DEVICE_ID]/  // Format: Brand-Model-AndroidVersion
        ├── notifications/
        │   └── {auto-generated-doc-id}/
        │       ├── timestamp: long
        │       ├── packageName: string
        │       ├── appName: string
        │       ├── title: string
        │       ├── content: string
        │       └── deviceId: string
        └── locations/
            └── {auto-generated-doc-id}/
                ├── timestamp: long
                ├── latitude: double
                ├── longitude: double
                ├── accuracy: float
                └── deviceId: string
```

## FirebaseManager Implementation Rules

### Singleton Pattern
```kotlin
class FirebaseManager private constructor() {
    companion object {
        @Volatile
        private var INSTANCE: FirebaseManager? = null
        
        fun getInstance(): FirebaseManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: FirebaseManager().also { INSTANCE = it }
            }
        }
    }
}
```

### Initialization Requirements
- **Anonymous Authentication**: Always use anonymous auth for simplicity
- **Offline Persistence**: Enable Firestore offline persistence
- **Error Handling**: Comprehensive error handling with fallback
- **Logging**: Detailed logging for debugging

### Sync Operations
```kotlin
suspend fun syncNotification(context: Context, data: NotificationData): SyncResult {
    return try {
        // Ensure initialization
        if (!isInitialized && !initialize(context)) {
            return SyncResult.FAILED
        }
        
        // Add device ID
        val deviceId = DeviceIdManager.getDeviceId(context)
        val dataWithDeviceId = data.copy(deviceId = deviceId)
        
        // Sync to Firestore
        firestore.collection("devices")
            .document(deviceId)
            .collection("notifications")
            .add(dataWithDeviceId)
            .await()
            
        SyncResult.SYNCED
    } catch (e: Exception) {
        Log.e(TAG, "Sync failed: ${e.message}", e)
        SyncResult.FAILED
    }
}
```

## Remote Config Implementation Rules

### Configuration Parameters
- **location_interval_minutes**: Location capture interval (5-120 minutes)
- **excluded_notification_packages**: JSON array of package names to filter

### Default Values
```kotlin
val defaults = mapOf(
    "location_interval_minutes" to 15L,
    "excluded_notification_packages" to "[]"
)
```

### Config Change Listeners
```kotlin
// Register listeners for real-time updates
remoteConfigManager.addLocationIntervalListener { newInterval ->
    Log.i(TAG, "Location interval changed to $newInterval minutes")
    rescheduleLocationWork(newInterval)
}

remoteConfigManager.addPackageFilterListener { newPackages ->
    Log.i(TAG, "Package filter updated: ${newPackages.size} excluded packages")
    updatePackageFilter(newPackages)
}
```

### Validation and Constraints
```kotlin
fun getLocationIntervalMinutes(): Long {
    val value = remoteConfig.getLong("location_interval_minutes")
    // Apply constraints: 5-120 minutes
    return min(max(value, 5L), 120L)
}
```

## Device ID Management Rules

### Device ID Format
- **Pattern**: `Brand-Model-AndroidVersion`
- **Examples**: `Samsung-Galaxy-S21-Android13`, `Xiaomi-Redmi-Note-10-Android11`
- **Sanitization**: Remove special characters, limit length, capitalize

### Implementation
```kotlin
private fun generateDeviceId(): String {
    val brand = sanitizeString(Build.BRAND)
    val model = sanitizeString(Build.MODEL)
    val androidVersion = "Android${Build.VERSION.SDK_INT}"
    return "$brand-$model-$androidVersion"
}

private fun sanitizeString(input: String): String {
    return input
        .replace(Regex("[^a-zA-Z0-9]"), "")
        .take(20)
        .lowercase()
        .replaceFirstChar { it.uppercase() }
        .ifEmpty { "Unknown" }
}
```

### Persistence
- Store device ID in SharedPreferences for consistency
- Cache in memory for performance
- Generate once per app installation

## Error Handling Rules

### Firebase Initialization Failures
```kotlin
suspend fun initialize(context: Context): Boolean {
    return try {
        // Enable offline persistence
        val settings = FirebaseFirestoreSettings.Builder()
            .setPersistenceEnabled(true)
            .build()
        firestore.firestoreSettings = settings
        
        // Anonymous authentication
        if (auth.currentUser == null) {
            auth.signInAnonymously().await()
        }
        
        isInitialized = true
        Log.i(TAG, "Firebase initialized successfully")
        true
    } catch (e: Exception) {
        Log.e(TAG, "Firebase initialization failed: ${e.message}", e)
        false
    }
}
```

### Sync Failure Handling
- **Continue Logging**: Always log to Logcat even if Firebase sync fails
- **Return Status**: Use SyncResult enum to indicate success/failure
- **Offline Queue**: Rely on Firestore's built-in offline persistence
- **No Retry Logic**: Let Firestore handle retries automatically

### Remote Config Failures
- **Use Cached Values**: Fall back to SharedPreferences cache
- **Use Defaults**: Fall back to hardcoded defaults if cache unavailable
- **Graceful Degradation**: App continues functioning with last known config
- **Retry Mechanism**: Automatic retry through WorkManager

## Performance Optimization Rules

### Coroutine Usage
```kotlin
// Use appropriate coroutine scope
private val serviceScope = CoroutineScope(Dispatchers.IO)

// Launch Firebase operations asynchronously
serviceScope.launch {
    val result = firebaseManager.syncData(context, data)
    handleSyncResult(result)
}
```

### Batching Operations
- **Individual Sync**: Sync each notification/location individually
- **No Batching**: Keep operations simple and immediate
- **Offline Queue**: Let Firestore handle batching internally

### Memory Management
- **Singleton Pattern**: Use singleton for managers to avoid multiple instances
- **Weak References**: Use weak references for listeners if needed
- **Cleanup**: Properly clean up resources in onDestroy methods

## Security Rules

### Firestore Security Rules
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow authenticated users to read/write their own device data
    match /devices/{deviceId}/{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

### Authentication
- **Anonymous Only**: Use anonymous authentication for simplicity
- **No User Data**: Don't collect or store personal user information
- **Device-Based**: Organize data by device ID, not user ID

## Monitoring and Analytics

### Firebase Analytics Events
```kotlin
// Optional: Track key events
firebaseAnalytics.logEvent("notification_captured") {
    param("app_name", appName)
    param("sync_status", syncStatus)
}

firebaseAnalytics.logEvent("location_captured") {
    param("accuracy", accuracy)
    param("sync_status", syncStatus)
}
```

### Performance Monitoring
- Monitor Firebase operation latency
- Track sync success/failure rates
- Monitor Remote Config fetch frequency
- Track offline queue size

## Configuration Management

### Remote Config Parameters
```json
{
  "location_interval_minutes": 15,
  "excluded_notification_packages": [
    "com.android.systemui",
    "com.android.launcher"
  ]
}
```

### Config Refresh Strategy
- **Periodic Refresh**: Every 12 hours via WorkManager
- **App Start**: Attempt fetch on app initialization
- **Manual Trigger**: Support manual refresh for testing
- **Cache Duration**: 1 hour minimum fetch interval

### Config Validation
```kotlin
fun validateLocationInterval(value: Long): Long {
    return when {
        value < 5 -> 5L
        value > 120 -> 120L
        else -> value
    }
}

fun validatePackageList(jsonString: String): List<String> {
    return try {
        val jsonArray = JSONArray(jsonString)
        (0 until jsonArray.length()).map { jsonArray.getString(it) }
    } catch (e: JSONException) {
        Log.w(TAG, "Invalid package list JSON: ${e.message}")
        emptyList()
    }
}
```

## Testing Firebase Integration

### Unit Tests
- Mock Firebase dependencies
- Test error handling paths
- Verify data model serialization
- Test config validation logic

### Integration Tests
- Test actual Firebase connectivity
- Verify offline behavior
- Test authentication flow
- Validate data structure in Firestore

### Manual Testing
- Monitor Firebase Console for real-time data
- Test network connectivity scenarios
- Verify config changes take effect
- Check offline sync behavior

## Troubleshooting Common Issues

### Firebase Connection Issues
- Verify google-services.json is in app/ directory
- Check Firebase project configuration
- Ensure internet permissions are granted
- Verify Firebase services are enabled

### Authentication Failures
- Check anonymous authentication is enabled
- Verify Firebase Auth rules
- Monitor authentication logs
- Test with fresh app installation

### Sync Failures
- Check Firestore security rules
- Verify network connectivity
- Monitor offline queue status
- Check device storage space

### Remote Config Issues
- Verify config parameters exist in Firebase Console
- Check fetch interval settings
- Ensure config is published
- Monitor config fetch logs
