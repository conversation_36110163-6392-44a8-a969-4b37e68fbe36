# NotifSync Lite - Development Guidelines

## Core Development Principles

### 1. Preserve Existing Functionality
- **NEVER BREAK**: Any change must preserve ALL existing functionality
- **Additive Changes**: New features should be additive, not replacements
- **Backward Compatibility**: Maintain compatibility with previous phases
- **Graceful Degradation**: New features should fail gracefully without affecting core functionality

### 2. Comprehensive Logging
- **Debug-First**: Extensive logging for debugging and monitoring
- **Consistent Format**: Use established log prefixes and formats
- **Error Details**: Include exception details in error logs
- **Status Reporting**: Log sync status and operation results

### 3. Headless Operation
- **No UI**: Maintain completely headless operation
- **Background Services**: Use services and workers for all functionality
- **Minimal Activity**: MainActivity only for initial setup, then finishes

## Code Standards

### Logging Guidelines
```kotlin
// Use consistent log tags and prefixes
companion object {
    private const val TAG = "COMPONENT_NAME"
    private const val LOG_PREFIX = "[COMPONENT]"
}

// Standard logging format
Log.i(TAG, "$LOG_PREFIX Operation completed successfully")
Log.e(TAG, "$LOG_PREFIX Error occurred: ${e.message}", e)
```

### Error Handling
```kotlin
// Always handle exceptions gracefully
try {
    // Operation
    Log.i(TAG, "$LOG_PREFIX Operation successful")
} catch (e: Exception) {
    Log.e(TAG, "$LOG_PREFIX Operation failed: ${e.message}", e)
    // Continue with fallback behavior
}
```

### Firebase Integration
```kotlin
// Use coroutines for Firebase operations
serviceScope.launch {
    try {
        val result = firebaseManager.syncData(context, data)
        val status = when (result) {
            SyncResult.SYNCED -> "SYNCED"
            SyncResult.FAILED -> "SYNC_FAILED"
        }
        Log.i(TAG, "$baseLogMessage | $status")
    } catch (e: Exception) {
        Log.i(TAG, "$baseLogMessage | SYNC_ERROR")
        Log.e(TAG, "$LOG_PREFIX Sync error: ${e.message}", e)
    }
}
```

## Component-Specific Rules

### NotificationCaptureService
- **Extend**: NotificationListenerService
- **Log Format**: `[NOTIFICATION] timestamp | AppName | Title | Content | SYNC_STATUS`
- **Firebase Sync**: Async sync with status reporting
- **Filtering**: Apply Remote Config package filtering
- **Lifecycle**: Handle connect/disconnect events

### LocationWorker
- **Extend**: CoroutineWorker
- **Interval**: Dynamic from Remote Config (5-120 minutes)
- **Log Format**: `[LOCATION] timestamp | Lat: X, Lng: Y | Accuracy: Zm | SYNC_STATUS`
- **Permissions**: Check location permissions before operation
- **Error Handling**: Graceful handling of location failures

### FirebaseManager
- **Pattern**: Singleton with thread-safe initialization
- **Authentication**: Anonymous authentication
- **Offline**: Enable Firestore offline persistence
- **Error Handling**: Return SyncResult enum for status
- **Device ID**: Use DeviceIdManager for consistent device identification

### RemoteConfigManager
- **Pattern**: Singleton with change listeners
- **Caching**: Cache values in SharedPreferences
- **Validation**: Apply constraints to config values
- **Refresh**: Periodic refresh every 12 hours
- **Listeners**: Notify components of config changes

## Testing Guidelines

### Unit Testing
- Test core logic in isolation
- Mock Firebase dependencies
- Verify error handling paths
- Test config validation logic

### Integration Testing
- Test Firebase sync functionality
- Verify offline behavior
- Test permission handling
- Validate WorkManager scheduling

### Manual Testing
- Install on physical device
- Grant all required permissions
- Monitor logs for expected output
- Test network connectivity scenarios
- Verify Firebase Console data

## Performance Guidelines

### Memory Management
- Use singleton pattern for managers
- Clean up listeners and callbacks
- Avoid memory leaks in services
- Cache frequently accessed data

### Battery Optimization
- Use efficient location settings
- Respect WorkManager constraints
- Minimize wake-ups and network calls
- Use foreground service appropriately

### Network Usage
- Batch Firebase operations when possible
- Use Firestore offline persistence
- Handle network failures gracefully
- Minimize Remote Config fetch frequency

## Security Guidelines

### Data Privacy
- Use anonymous Firebase authentication
- Store minimal device information
- Respect user privacy in logging
- Follow Android privacy guidelines

### Permissions
- Request only necessary permissions
- Handle permission denials gracefully
- Provide clear permission explanations
- Follow Android permission best practices

## Build and Deployment

### Build Configuration
- Use version catalogs for dependency management
- Maintain consistent build settings
- Enable ProGuard for release builds
- Test both debug and release builds

### Firebase Setup
- Ensure google-services.json is properly configured
- Verify Firebase project settings
- Test Firebase connectivity
- Monitor Firebase usage and quotas

### Documentation
- Update README.md for any changes
- Maintain architecture documentation
- Document new features and APIs
- Provide troubleshooting guides

## Common Patterns

### Service Initialization
```kotlin
override fun onCreate() {
    super.onCreate()
    Log.i(TAG, "$LOG_PREFIX Service created")
    
    serviceScope.launch {
        try {
            initializeComponents()
            Log.i(TAG, "$LOG_PREFIX Initialization completed")
        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX Initialization failed: ${e.message}", e)
        }
    }
}
```

### WorkManager Scheduling
```kotlin
val constraints = Constraints.Builder()
    .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
    .setRequiresBatteryNotLow(false)
    .build()

val workRequest = PeriodicWorkRequestBuilder<WorkerClass>(
    intervalMinutes, TimeUnit.MINUTES
).setConstraints(constraints).build()

WorkManager.getInstance(context).enqueueUniquePeriodicWork(
    WORK_NAME,
    ExistingPeriodicWorkPolicy.REPLACE,
    workRequest
)
```

### Remote Config Listeners
```kotlin
remoteConfigManager.addConfigListener { newValue ->
    Log.i(TAG, "$LOG_PREFIX Config updated: $newValue")
    applyNewConfiguration(newValue)
}
```

## Troubleshooting

### Common Issues
- **Notification Access**: Ensure service is enabled in settings
- **Location Permissions**: Verify background location access
- **Firebase Sync**: Check network connectivity and authentication
- **WorkManager**: Verify constraints and scheduling
- **Remote Config**: Check fetch intervals and network access

### Debugging Tools
- Use `adb logcat` with appropriate filters
- Monitor Firebase Console for real-time data
- Check WorkManager status in device settings
- Use Android Studio debugger for complex issues
