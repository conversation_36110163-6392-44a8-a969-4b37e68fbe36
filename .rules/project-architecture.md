# NotifSync Lite - Project Architecture Rules

## Project Overview
NotifSync Lite is a **headless Android application** that captures ALL device notifications and location data, logging to Android Logcat and syncing to Firebase Firestore. The app operates completely in the background without any user interface.

## Core Architecture Principles

### 1. Headless Operation
- **NO UI COMPONENTS**: The app runs entirely in the background
- **Minimal MainActivity**: Only for initial permission setup, then finishes immediately
- **Service-Based**: Core functionality runs through Android services and workers

### 2. Multi-Phase Evolution
- **Phase 1**: Notification capture with local logging
- **Phase 2**: Added location tracking every 15 minutes
- **Phase 3**: Added Firebase Firestore sync while preserving all existing functionality
- **Phase 4**: Added Firebase Remote Config for dynamic configuration

### 3. Fail-Safe Design
- **Graceful Degradation**: App continues working even if Firebase fails
- **Preserved Functionality**: Each phase preserves ALL previous functionality
- **Comprehensive Logging**: Detailed logging for debugging and monitoring

## Package Structure

```
com.ab.notif/
├── NotifSyncApplication.kt      # Application class, WorkManager initialization
├── MainActivity.kt              # Minimal setup activity for permissions
├── NotificationCaptureService.kt # Main notification listener service
├── LocationWorker.kt            # WorkManager worker for location capture
├── LocationService.kt           # Foreground service for background location
├── BootReceiver.kt              # Auto-start on device boot
├── ConfigRefreshWorker.kt       # Remote Config refresh worker
├── data/
│   ├── NotificationData.kt      # Firebase data model for notifications
│   └── LocationData.kt          # Firebase data model for locations
├── firebase/
│   ├── FirebaseManager.kt       # Firebase integration and sync manager
│   └── RemoteConfigManager.kt   # Firebase Remote Config manager
└── utils/
    └── DeviceIdManager.kt       # Device ID generation and management
```

## Core Components

### NotifSyncApplication
- **Purpose**: Application-level initialization
- **Responsibilities**:
  - Initialize WorkManager for location tracking
  - Start LocationService for background location access
  - Initialize Firebase Remote Config
  - Schedule periodic config refresh
- **Key Features**:
  - Dynamic location interval from Remote Config
  - Config change listeners for real-time updates

### NotificationCaptureService
- **Type**: NotificationListenerService
- **Purpose**: Capture ALL device notifications
- **Responsibilities**:
  - Listen for notification events
  - Extract notification details (app, title, content)
  - Log to Android Logcat with timestamp
  - Sync to Firebase Firestore
  - Filter notifications based on Remote Config
- **Key Features**:
  - Enhanced logging with sync status
  - Package filtering via Remote Config
  - Graceful Firebase sync failure handling

### LocationWorker
- **Type**: CoroutineWorker (WorkManager)
- **Purpose**: Periodic location capture
- **Responsibilities**:
  - Capture GPS location using FusedLocationProviderClient
  - Log location data with accuracy
  - Sync to Firebase Firestore
  - Handle location permission checks
- **Key Features**:
  - Dynamic interval from Remote Config (5-120 minutes)
  - Battery-optimized location requests
  - Comprehensive error handling

### LocationService
- **Type**: Foreground Service
- **Purpose**: Maintain background location access
- **Responsibilities**:
  - Show persistent notification for location tracking
  - Ensure location capture continues in background
  - Handle service lifecycle properly

### FirebaseManager
- **Pattern**: Singleton
- **Purpose**: Firebase integration and sync
- **Responsibilities**:
  - Anonymous authentication
  - Firestore operations with offline persistence
  - Device ID management
  - Sync status reporting
- **Key Features**:
  - Offline sync queue
  - Comprehensive error handling
  - Coroutine-based async operations

### RemoteConfigManager
- **Pattern**: Singleton
- **Purpose**: Dynamic configuration management
- **Responsibilities**:
  - Fetch and cache Remote Config values
  - Notify listeners of config changes
  - Validate config constraints
  - Schedule periodic refresh
- **Key Features**:
  - Real-time config updates
  - Cached fallback values
  - Config change listeners

## Data Models

### NotificationData
```kotlin
data class NotificationData(
    val timestamp: Long,
    val packageName: String,
    val appName: String,
    val title: String,
    val content: String,
    val deviceId: String
)
```

### LocationData
```kotlin
data class LocationData(
    val timestamp: Long,
    val latitude: Double,
    val longitude: Double,
    val accuracy: Float,
    val deviceId: String
)
```

## Firebase Structure
```
firestore-root/
└── devices/
    └── [DEVICE_ID]/  // Format: Brand-Model-AndroidVersion
        ├── notifications/
        │   └── {auto-generated-doc-id}/
        └── locations/
            └── {auto-generated-doc-id}/
```

## Key Dependencies
- **WorkManager**: Periodic location tracking
- **Google Play Services Location**: GPS location access
- **Firebase BOM**: Version management for Firebase libraries
- **Firebase Firestore**: Cloud data storage
- **Firebase Auth**: Anonymous authentication
- **Firebase Remote Config**: Dynamic configuration

## Permissions Required
- `BIND_NOTIFICATION_LISTENER_SERVICE`: Notification access
- `RECEIVE_BOOT_COMPLETED`: Auto-start on boot
- `FOREGROUND_SERVICE`: Background service operation
- `ACCESS_FINE_LOCATION`: Precise location access
- `ACCESS_BACKGROUND_LOCATION`: Background location access
- `FOREGROUND_SERVICE_LOCATION`: Location foreground service
- `INTERNET`: Firebase sync
- `ACCESS_NETWORK_STATE`: Network connectivity checking

## Logging Standards
- **Notification Format**: `[NOTIFICATION] timestamp | AppName | Title | Content | SYNC_STATUS`
- **Location Format**: `[LOCATION] timestamp | Lat: X, Lng: Y | Accuracy: Zm | SYNC_STATUS`
- **Firebase Format**: `[FIREBASE] operation details`
- **Config Format**: `[CONFIG] configuration changes`
- **Application Format**: `[APPLICATION] lifecycle events`

## Build Configuration
- **Target SDK**: 36 (Android 14)
- **Min SDK**: 31 (Android 12)
- **Kotlin Version**: 2.0.21
- **AGP Version**: 8.13.0
- **Java Version**: 11
