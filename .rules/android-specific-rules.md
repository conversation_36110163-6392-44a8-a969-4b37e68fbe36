# NotifSync Lite - Android-Specific Development Rules

## Android Components Usage

### Services and Workers
- **NotificationListenerService**: For notification capture (requires special permission)
- **Foreground Service**: For background location access (requires notification)
- **WorkManager**: For periodic location capture (battery optimized)
- **BroadcastReceiver**: For boot-time auto-start

### Service Lifecycle Management
```kotlin
// NotificationListenerService
override fun onListenerConnected() {
    super.onListenerConnected()
    Log.i(TAG, "Service connected and ready")
}

override fun onListenerDisconnected() {
    super.onListenerDisconnected()
    Log.w(TAG, "Service disconnected")
    // Service will auto-reconnect if enabled in settings
}

// Foreground Service
override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
    startForeground(NOTIFICATION_ID, createNotification())
    return START_STICKY // Restart if killed by system
}
```

## Permission Management

### Required Permissions
```xml
<!-- Notification access (special permission) -->
<uses-permission android:name="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" />

<!-- Boot receiver -->
<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />

<!-- Foreground service -->
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />

<!-- Location permissions -->
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />

<!-- Network permissions -->
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
```

### Permission Handling Strategy
```kotlin
// Check notification access
private fun isNotificationAccessGranted(context: Context): Boolean {
    return NotificationManagerCompat.getEnabledListenerPackages(context)
        .contains(context.packageName)
}

// Request location permissions
private fun requestLocationPermissions() {
    val permissionsToRequest = mutableListOf<String>()
    
    if (ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION)
        != PackageManager.PERMISSION_GRANTED) {
        permissionsToRequest.add(Manifest.permission.ACCESS_FINE_LOCATION)
    }
    
    if (permissionsToRequest.isNotEmpty()) {
        locationPermissionLauncher.launch(permissionsToRequest.toTypedArray())
    }
}
```

## WorkManager Implementation

### Constraints Configuration
```kotlin
val constraints = Constraints.Builder()
    .setRequiredNetworkType(NetworkType.NOT_REQUIRED) // Work offline
    .setRequiresBatteryNotLow(false) // Work even on low battery
    .setRequiresCharging(false) // Work when not charging
    .setRequiresDeviceIdle(false) // Work when device is active
    .setRequiresStorageNotLow(false) // Work with low storage
    .build()
```

### Periodic Work Scheduling
```kotlin
val locationWorkRequest = PeriodicWorkRequestBuilder<LocationWorker>(
    intervalMinutes, TimeUnit.MINUTES,
    5, TimeUnit.MINUTES // Flex interval for battery optimization
)
    .setConstraints(constraints)
    .setBackoffCriteria(
        BackoffPolicy.LINEAR,
        WorkRequest.MIN_BACKOFF_MILLIS,
        TimeUnit.MILLISECONDS
    )
    .build()

WorkManager.getInstance(context).enqueueUniquePeriodicWork(
    LocationWorker.WORK_NAME,
    ExistingPeriodicWorkPolicy.REPLACE, // Replace to update interval
    locationWorkRequest
)
```

### Worker Implementation
```kotlin
class LocationWorker(context: Context, params: WorkerParameters) : CoroutineWorker(context, params) {
    
    override suspend fun doWork(): Result {
        return try {
            if (!hasLocationPermissions()) {
                Log.w(TAG, "Location permissions not granted")
                return Result.success() // Don't retry, wait for permissions
            }
            
            val location = getCurrentLocation()
            if (location != null) {
                logAndSyncLocation(location)
            }
            
            Result.success()
        } catch (e: Exception) {
            Log.e(TAG, "LocationWorker error: ${e.message}", e)
            Result.failure() // Will retry with backoff
        }
    }
}
```

## Location Services

### FusedLocationProviderClient Usage
```kotlin
private suspend fun getCurrentLocation(): Location? {
    return suspendCoroutine { continuation ->
        try {
            fusedLocationClient.getCurrentLocation(
                Priority.PRIORITY_BALANCED_POWER_ACCURACY, // Battery optimized
                null
            ).addOnSuccessListener { location ->
                continuation.resume(location)
            }.addOnFailureListener { exception ->
                Log.e(TAG, "Location request failed: ${exception.message}", exception)
                continuation.resume(null)
            }
        } catch (e: SecurityException) {
            Log.e(TAG, "Security exception: ${e.message}", e)
            continuation.resume(null)
        }
    }
}
```

### Foreground Service for Location
```kotlin
class LocationService : Service() {
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        val notification = createNotification()
        startForeground(NOTIFICATION_ID, notification)
        return START_STICKY
    }
    
    private fun createNotification(): Notification {
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("NotifSync Location Tracking")
            .setContentText("Capturing location every 15 minutes")
            .setSmallIcon(android.R.drawable.ic_menu_mylocation)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setOngoing(true)
            .setAutoCancel(false)
            .build()
    }
}
```

## Notification Handling

### NotificationListenerService Implementation
```kotlin
override fun onNotificationPosted(sbn: StatusBarNotification?) {
    super.onNotificationPosted(sbn)
    
    if (sbn == null) return
    
    try {
        // Check if package should be filtered
        if (shouldFilterNotification(sbn.packageName)) {
            logFilteredNotification(sbn)
            return
        }
        
        logAndSyncNotification(sbn)
    } catch (e: Exception) {
        Log.e(TAG, "Error processing notification: ${e.message}", e)
    }
}
```

### Notification Data Extraction
```kotlin
private fun extractNotificationData(sbn: StatusBarNotification): NotificationData? {
    val notification = sbn.notification ?: return null
    val extras = notification.extras ?: return null
    
    val packageName = sbn.packageName ?: "Unknown"
    val appName = getAppName(packageName)
    val title = extras.getCharSequence(Notification.EXTRA_TITLE)?.toString() ?: "No Title"
    val content = extras.getCharSequence(Notification.EXTRA_TEXT)?.toString() ?: "No Content"
    val bigText = extras.getCharSequence(Notification.EXTRA_BIG_TEXT)?.toString()
    
    // Use big text if available and different from regular content
    val finalContent = if (!bigText.isNullOrEmpty() && bigText != content) {
        bigText
    } else {
        content
    }
    
    return NotificationData.create(packageName, appName, title, finalContent, "")
}
```

## Boot Receiver Implementation

### Auto-Start Configuration
```kotlin
class BootReceiver : BroadcastReceiver() {
    
    override fun onReceive(context: Context?, intent: Intent?) {
        if (context == null || intent == null) return
        
        when (intent.action) {
            Intent.ACTION_BOOT_COMPLETED -> {
                Log.i(TAG, "Device boot completed")
                // NotificationListenerService auto-starts if enabled
                // WorkManager auto-resumes scheduled work
            }
            Intent.ACTION_MY_PACKAGE_REPLACED -> {
                Log.i(TAG, "App package replaced")
                // Services will restart automatically
            }
        }
    }
}
```

### Manifest Configuration
```xml
<receiver
    android:name=".BootReceiver"
    android:enabled="true"
    android:exported="true">
    <intent-filter android:priority="1000">
        <action android:name="android.intent.action.BOOT_COMPLETED" />
        <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
        <action android:name="android.intent.action.PACKAGE_REPLACED" />
        <data android:scheme="package" />
    </intent-filter>
</receiver>
```

## Battery Optimization

### Doze Mode and App Standby
- **WorkManager**: Automatically handles Doze mode constraints
- **Foreground Service**: Exempt from Doze mode restrictions
- **Battery Optimization**: Request user to whitelist app if needed

### Background Execution Limits
- **Target SDK 31+**: Strict background execution limits
- **Foreground Service**: Required for background location access
- **WorkManager**: Preferred for periodic tasks
- **Notification Access**: Special permission allows background execution

## Testing on Physical Devices

### Device Requirements
- **Android 12+**: Minimum API level 31
- **Physical Device**: Required for accurate testing
- **Location Services**: Must be enabled
- **Battery Optimization**: May need to be disabled for testing

### Testing Scenarios
```kotlin
// Test notification capture
// 1. Send test notifications from various apps
// 2. Verify logs appear in Logcat
// 3. Check Firebase Console for synced data

// Test location capture
// 1. Wait for scheduled location capture (15 minutes default)
// 2. Verify location logs with coordinates
// 3. Check Firebase Console for location data

// Test offline behavior
// 1. Disable network connectivity
// 2. Generate notifications and trigger location capture
// 3. Re-enable network and verify sync
```

### Debug Commands
```bash
# View all app logs
adb logcat -s NOTIFICATION_CAPTURE -s LOCATION_CAPTURE -s FIREBASE_MANAGER

# View only notifications
adb logcat -s NOTIFICATION_CAPTURE

# View only locations
adb logcat -s LOCATION_CAPTURE

# View WorkManager status
adb shell dumpsys jobscheduler | grep com.ab.notif

# Check notification access
adb shell cmd notification list_listeners
```

## Performance Considerations

### Memory Usage
- Use singleton pattern for managers
- Avoid memory leaks in long-running services
- Clean up resources in onDestroy methods
- Monitor memory usage during testing

### CPU Usage
- Use coroutines for async operations
- Avoid blocking main thread
- Minimize processing in notification callbacks
- Use efficient location request settings

### Network Usage
- Batch Firebase operations when possible
- Use Firestore offline persistence
- Minimize Remote Config fetch frequency
- Handle network failures gracefully

### Storage Usage
- Firestore offline cache grows over time
- Monitor local storage usage
- Consider data retention policies
- Clean up old cached data if needed
