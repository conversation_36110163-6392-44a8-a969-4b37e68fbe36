# NotifSync Lite - AI Agent Rules

This directory contains comprehensive rules and guidelines for AI agents working on the NotifSync Lite Android project. These rules ensure consistent development practices, maintain project architecture integrity, and preserve the headless notification and location capture functionality.

## Rule Files Overview

### 1. [project-architecture.md](./project-architecture.md)
**Core project architecture and component overview**
- Project structure and package organization
- Component responsibilities and relationships
- Data models and Firebase structure
- Key dependencies and build configuration
- Logging standards and conventions

### 2. [development-guidelines.md](./development-guidelines.md)
**Development standards and best practices**
- Core development principles (preserve functionality, comprehensive logging)
- Code standards and patterns
- Component-specific implementation rules
- Testing guidelines and performance optimization
- Security guidelines and common patterns

### 3. [firebase-integration.md](./firebase-integration.md)
**Firebase services integration rules**
- Firebase architecture and data structure
- FirebaseManager and RemoteConfigManager implementation
- Device ID management and error handling
- Performance optimization and security rules
- Configuration management and troubleshooting

### 4. [android-specific-rules.md](./android-specific-rules.md)
**Android platform-specific implementation guidelines**
- Android components usage (Services, Workers, Receivers)
- Permission management and handling strategies
- WorkManager implementation and location services
- Notification handling and boot receiver setup
- Battery optimization and performance considerations

## Key Principles for AI Agents

### 🚫 Never Break Existing Functionality
- **Preserve ALL existing features** from previous phases
- **Additive changes only** - never replace working functionality
- **Graceful degradation** - new features must fail safely
- **Comprehensive testing** before any changes

### 📝 Maintain Comprehensive Logging
- **Use established log formats** and prefixes
- **Include sync status** in all operations
- **Detailed error logging** with exception details
- **Consistent timestamp formatting**

### 🔧 Follow Established Patterns
- **Singleton pattern** for managers (FirebaseManager, RemoteConfigManager)
- **Coroutines** for async operations
- **Error handling** with try-catch and fallback behavior
- **Service lifecycle** management

### 🏗️ Respect Architecture
- **Headless operation** - no UI components
- **Service-based** functionality
- **Firebase integration** with offline support
- **Remote Config** for dynamic configuration

## Project Context

### Current State (Phase 4)
- ✅ **Phase 1**: Notification capture with local logging
- ✅ **Phase 2**: Location tracking every 15 minutes
- ✅ **Phase 3**: Firebase Firestore sync with offline support
- ✅ **Phase 4**: Firebase Remote Config for dynamic configuration

### Core Functionality
- **Headless Android app** that runs entirely in background
- **Captures ALL notifications** from any app on device
- **Tracks location** every 15 minutes (configurable via Remote Config)
- **Syncs to Firebase Firestore** with offline queue support
- **Dynamic configuration** via Firebase Remote Config
- **Comprehensive logging** to Android Logcat

### Key Components
- **NotificationCaptureService**: NotificationListenerService for notification capture
- **LocationWorker**: WorkManager worker for periodic location capture
- **LocationService**: Foreground service for background location access
- **FirebaseManager**: Singleton for Firebase operations and sync
- **RemoteConfigManager**: Singleton for dynamic configuration management
- **DeviceIdManager**: Utility for consistent device identification

## Development Workflow

### Before Making Changes
1. **Review existing functionality** to understand current behavior
2. **Check logs** to understand expected output format
3. **Identify integration points** with Firebase and Remote Config
4. **Plan additive changes** that preserve existing functionality

### During Development
1. **Follow established patterns** from existing code
2. **Use consistent logging** with appropriate prefixes
3. **Handle errors gracefully** with fallback behavior
4. **Test incrementally** to ensure no regression

### After Changes
1. **Verify all existing functionality** still works
2. **Check log output** matches expected format
3. **Test Firebase sync** and offline behavior
4. **Validate Remote Config** integration if applicable

## Common Tasks and Patterns

### Adding New Notification Filtering
```kotlin
// In NotificationCaptureService
private fun shouldFilterNotification(packageName: String): Boolean {
    return excludedPackages.contains(packageName)
}

private fun logFilteredNotification(sbn: StatusBarNotification) {
    val logMessage = "$FILTERED_LOG_PREFIX $timestamp | $packageName | $appName | $title | EXCLUDED"
    Log.i(TAG, logMessage)
}
```

### Adding New Remote Config Parameter
```kotlin
// In RemoteConfigManager
companion object {
    const val KEY_NEW_PARAMETER = "new_parameter"
    const val DEFAULT_NEW_PARAMETER = "default_value"
}

fun getNewParameter(): String {
    return if (isInitialized) {
        remoteConfig.getString(KEY_NEW_PARAMETER)
    } else {
        sharedPrefs.getString(PREF_NEW_PARAMETER, DEFAULT_NEW_PARAMETER) ?: DEFAULT_NEW_PARAMETER
    }
}
```

### Adding New Firebase Sync Operation
```kotlin
// In FirebaseManager
suspend fun syncNewData(context: Context, data: NewData): SyncResult {
    return try {
        if (!isInitialized && !initialize(context)) {
            return SyncResult.FAILED
        }
        
        val deviceId = DeviceIdManager.getDeviceId(context)
        val dataWithDeviceId = data.copy(deviceId = deviceId)
        
        firestore.collection("devices")
            .document(deviceId)
            .collection("new_collection")
            .add(dataWithDeviceId)
            .await()
            
        SyncResult.SYNCED
    } catch (e: Exception) {
        Log.e(TAG, "Sync failed: ${e.message}", e)
        SyncResult.FAILED
    }
}
```

## Testing Guidelines

### Manual Testing Checklist
- [ ] App installs and starts successfully
- [ ] Notification access permission granted
- [ ] Location permissions granted (including background)
- [ ] Notifications appear in Logcat with correct format
- [ ] Location captured every configured interval
- [ ] Firebase sync status appears in logs
- [ ] Remote Config changes take effect
- [ ] Offline behavior works correctly

### Log Monitoring Commands
```bash
# Monitor all app logs
adb logcat -s NOTIFICATION_CAPTURE -s LOCATION_CAPTURE -s FIREBASE_MANAGER -s REMOTE_CONFIG

# Monitor specific component
adb logcat -s NOTIFICATION_CAPTURE

# Monitor config changes
adb logcat -s REMOTE_CONFIG -s CONFIG_REFRESH
```

## Troubleshooting

### Common Issues
- **Notification access not granted**: Check Settings > Apps > Special access > Notification access
- **Location not captured**: Verify location permissions and background location access
- **Firebase sync failing**: Check network connectivity and Firebase configuration
- **Remote Config not updating**: Verify config is published in Firebase Console

### Debug Information
- **Device ID format**: Brand-Model-AndroidVersion (e.g., Samsung-Galaxy-S21-Android13)
- **Log prefixes**: [NOTIFICATION], [LOCATION], [FIREBASE], [CONFIG], [APPLICATION]
- **Sync statuses**: SYNCED, SYNC_FAILED, SYNC_ERROR
- **Firebase structure**: devices/[DEVICE_ID]/notifications/ and devices/[DEVICE_ID]/locations/

These rules ensure that any AI agent working on this project maintains the established architecture, preserves existing functionality, and follows consistent development practices.
