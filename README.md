# NotifSync Lite - Phase 3

A headless Android app that captures ALL device notifications AND location data, logging both to Android Logcat AND syncing to Firebase Firestore for cloud storage and monitoring.

## Features

- ✅ **Completely headless** - No UI, runs entirely in background
- ✅ **Captures ALL notifications** from any app on the device
- ✅ **Location tracking** - Captures GPS location every 15 minutes
- ✅ **Firebase sync** - Syncs all data to Firestore cloud database
- ✅ **Offline support** - Queues data when offline, syncs when connected
- ✅ **Enhanced logging** - Shows sync status for each notification and location
- ✅ **Auto-start** - Begins capturing immediately after installation and device boot
- ✅ **Battery optimized** - Uses efficient location settings and WorkManager

## Requirements

- Android 12+ (API 31+)
- Notification access permission (must be manually enabled)
- Location permissions including background location access (must be manually enabled)
- Firebase project with Firestore and Authentication enabled
- Internet connection for Firebase sync

## Quick Start

1. **Setup Firebase**
   - Follow [Firebase Setup Instructions](FIREBASE_SETUP.md)
   - Create Firebase project and add google-services.json
   - Enable Firestore and Anonymous Authentication

2. **Build & Install**
   ```bash
   ./gradlew assembleDebug
   adb install app/build/outputs/apk/debug/app-debug.apk
   ```

3. **Enable Permissions**
   - Launch the app (it will auto-open settings and request permissions)
   - Enable "Notif Sync Lite" in notification access settings
   - Grant location permissions and select "Allow all the time" for background location
   - App becomes headless after setup

4. **View Logs**
   ```bash
   # View all logs including Firebase sync status
   adb logcat -s NOTIFICATION_CAPTURE -s LOCATION_CAPTURE -s FIREBASE_MANAGER

   # View only notifications
   adb logcat -s NOTIFICATION_CAPTURE

   # View only location
   adb logcat -s LOCATION_CAPTURE
   ```

5. **Monitor Firebase Data**
   - Go to Firebase Console > Firestore Database
   - View real-time data sync under devices/[DEVICE_ID]/

## Log Format

### Notifications (with Firebase sync status)
```
[NOTIFICATION] 2024-01-15 14:30:25 | WhatsApp | John Doe | New message received | SYNCED
[NOTIFICATION] 2024-01-15 14:31:10 | Gmail | Google | You have 3 new emails | SYNC_FAILED
```

### Location (every 15 minutes, with Firebase sync status)
```
[LOCATION] 2024-01-15 14:30:30 | Lat: -6.2088, Lng: 106.8456 | Accuracy: 12.5m | SYNCED
[LOCATION] 2024-01-15 14:45:30 | Lat: -6.2090, Lng: 106.8460 | Accuracy: 8.2m | SYNCED
```

## Project Structure

```
app/src/main/java/com/ab/notif/
├── NotificationCaptureService.kt  # Main notification listener service + Firebase sync
├── LocationWorker.kt             # WorkManager worker for location capture + Firebase sync
├── LocationService.kt            # Foreground service for background location
├── BootReceiver.kt               # Auto-start on device boot
├── MainActivity.kt               # Minimal setup activity with permissions
├── NotifSyncApplication.kt       # Application class for WorkManager initialization
├── data/
│   ├── NotificationData.kt       # Firebase data model for notifications
│   └── LocationData.kt           # Firebase data model for locations
├── firebase/
│   └── FirebaseManager.kt        # Firebase integration and sync manager
└── utils/
    └── DeviceIdManager.kt        # Device ID generation and management
```

## Key Components

### NotificationCaptureService
- Extends `NotificationListenerService`
- Captures all device notifications
- Logs with detailed formatting
- Handles service lifecycle properly

### LocationWorker
- WorkManager worker that runs every 15 minutes
- Uses FusedLocationProviderClient for accurate location
- Handles location permission checks gracefully
- Logs location data in specified format

### LocationService
- Foreground service for background location access
- Maintains persistent notification for location tracking
- Ensures location capture continues in background

### BootReceiver
- Handles `BOOT_COMPLETED` broadcasts
- Ensures services start after device reboot
- Handles app updates and replacements

### MainActivity
- Minimal activity for initial setup
- Requests location permissions
- Opens notification access settings
- Finishes immediately to stay headless

### NotifSyncApplication
- Application class that initializes WorkManager
- Starts periodic location tracking
- Starts foreground location service

### FirebaseManager
- Handles Firebase authentication and Firestore operations
- Manages offline sync and error handling
- Provides sync status for enhanced logging

### Data Models
- **NotificationData**: Firestore-compatible notification data structure
- **LocationData**: Firestore-compatible location data structure

### DeviceIdManager
- Generates unique device ID in Brand-Model-AndroidVersion format
- Stores device ID in SharedPreferences for consistency

## Permissions

- `BIND_NOTIFICATION_LISTENER_SERVICE` - Required for notification capture
- `RECEIVE_BOOT_COMPLETED` - Enables auto-start after boot
- `FOREGROUND_SERVICE` - Allows background service operation
- `ACCESS_FINE_LOCATION` - Required for precise location capture
- `ACCESS_COARSE_LOCATION` - Fallback for basic location capture
- `ACCESS_BACKGROUND_LOCATION` - Required for location capture when app is not active
- `FOREGROUND_SERVICE_LOCATION` - Required for location foreground service
- `INTERNET` - Required for Firebase sync
- `ACCESS_NETWORK_STATE` - Required for network connectivity checking

## Development

### Building
```bash
./gradlew build
```

### Testing
```bash
./gradlew test
```

### Linting
```bash
./gradlew lint
```

## Documentation

- [Setup Instructions](SETUP_INSTRUCTIONS.md) - Detailed setup and usage guide
- [Firebase Setup](FIREBASE_SETUP.md) - Firebase project setup and configuration
- [Task Requirements](task-notif.md) - Original Phase 1 task specification
- [Phase 2 Requirements](task-location.md) - Location tracking task specification
- [Phase 3 Requirements](task-firebase.md) - Firebase integration task specification

## License

This project is for educational and debugging purposes. Use responsibly and in compliance with local privacy laws.
