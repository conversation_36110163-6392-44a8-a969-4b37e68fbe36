
**Task: Android App Phase 3 - Add Firebase Integration**

**Objective:**
Extend the existing Phase 1 & 2 app to sync all captured notifications and locations to Firebase Firestore. Keep ALL existing logging functionality intact.

**Requirements:**
1. **Keep ALL existing functionality** - notification capture and location tracking from Phase 1 & 2
2. **Keep ALL logging** - continue logging to Logcat exactly as before
3. **Add Firebase sync** - save notifications and locations to Firestore in addition to logging
4. **Offline handling** - queue data when offline, sync when connection restored
5. **No UI** - remains headless app

**Firebase Structure:**
```
firestore-root/
└── devices/
    └── [DEVICE_ID]/  // e.g., "Redmi-Note-10-Pro-Android12"
        ├── notifications/
        │   └── {auto-generated-doc-id}/
        │       ├── timestamp: long
        │       ├── package_name: string
        │       ├── app_name: string
        │       ├── title: string
        │       ├── content: string
        │       └── device_id: string
        └── locations/
            └── {auto-generated-doc-id}/
                ├── timestamp: long
                ├── latitude: double
                ├── longitude: double
                ├── accuracy: float
                └── device_id: string
```

**Device ID Generation:**
Create device ID using: `Brand-Model-AndroidVersion` format
Example: `Samsung-Galaxy-S21-Android13`, `Xiaomi-Redmi-Note-10-Android11`

**Technical Specifications:**
- Add Firebase Firestore SDK
- Generate unique device ID on first run, store in SharedPreferences
- Save to Firebase AND continue logging (don't replace logging)
- Handle network connectivity issues gracefully
- Use Firestore's offline persistence feature

**Additional Components Needed:**
1. Firebase Firestore integration
2. Device ID generator utility
3. Network connectivity checking
4. Firestore data models
5. Error handling for Firebase operations

**Logging Format (Enhanced):**
```
[NOTIFICATION] 2024-01-15 14:30:25 | WhatsApp | John Doe | New message received | SYNCED
[NOTIFICATION] 2024-01-15 14:30:26 | Gmail | Google | Email | SYNC_FAILED
[LOCATION] 2024-01-15 14:30:30 | Lat: -6.2088, Lng: 106.8456 | Accuracy: 12.5m | SYNCED
```

**Important Notes:**
- Don't remove any existing logging - ADD Firebase sync alongside it
- Handle Firebase authentication (anonymous auth is fine)
- Add basic retry mechanism for failed syncs
- Include google-services.json setup instructions
- Keep code simple - no complex offline queue management needed (use Firestore's built-in offline support)

**Deliverables:**
1. Updated Android Studio project with Firebase integration
2. All existing notification + location capture still working
3. Data syncing to Firestore with specified structure
4. Firebase setup instructions (google-services.json, dependencies)
5. Enhanced logging showing sync status
