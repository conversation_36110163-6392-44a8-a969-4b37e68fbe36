# Firebase Remote Config Setup Instructions

## Overview
This document provides step-by-step instructions for setting up Firebase Remote Config for the NotifSync Lite app to enable dynamic configuration of location intervals and notification filtering.

## Prerequisites
- Firebase project already set up (from Phase 3)
- Firebase console access
- App already connected to Firebase

## Firebase Console Configuration

### 1. Access Remote Config
1. Open [Firebase Console](https://console.firebase.google.com/)
2. Select your NotifSync Lite project
3. Navigate to **Remote Config** in the left sidebar
4. Click **Create configuration** if this is your first time

### 2. Add Configuration Parameters

#### Parameter 1: Location Interval
1. Click **Add parameter**
2. **Parameter key**: `location_interval_minutes`
3. **Default value**: `15`
4. **Description**: "Location capture interval in minutes (5-120)"
5. **Value type**: Number
6. Click **Save**

#### Parameter 2: Excluded Notification Packages
1. Click **Add parameter**
2. **Parameter key**: `excluded_notification_packages`
3. **Default value**: `["com.android.systemui"]`
4. **Description**: "JSON array of package names to exclude from notification capture"
5. **Value type**: JSON
6. Click **Save**

### 3. Publish Configuration
1. Review your parameters
2. Click **Publish changes**
3. Add a version description (e.g., "Initial Remote Config setup")
4. Click **Publish**

## Configuration Parameters Reference

### `location_interval_minutes`
- **Type**: Number (Long)
- **Default**: 15
- **Range**: 5-120 minutes
- **Description**: Controls how often the app captures location data
- **Example values**:
  - `5` - Every 5 minutes (high frequency)
  - `15` - Every 15 minutes (default)
  - `30` - Every 30 minutes (medium frequency)
  - `60` - Every hour (low frequency)

### `excluded_notification_packages`
- **Type**: JSON Array of Strings
- **Default**: `["com.android.systemui"]`
- **Description**: List of package names to exclude from notification capture
- **Example values**:
  ```json
  [
    "com.android.systemui",
    "com.banking.app",
    "com.privacy.messenger",
    "com.sensitive.app"
  ]
  ```

## Testing Configuration Changes

### 1. Test Location Interval Changes
1. In Firebase Console, change `location_interval_minutes` to `10`
2. Publish changes
3. Wait up to 1 hour for the app to fetch new config
4. Check Android Logcat for:
   ```
   [CONFIG] 2024-01-15 14:00:00 | Location interval updated: 10 minutes
   [APPLICATION] Location interval changed to 10 minutes, rescheduling work
   ```

### 2. Test Notification Filtering
1. In Firebase Console, update `excluded_notification_packages`:
   ```json
   ["com.android.systemui", "com.whatsapp", "com.example.testapp"]
   ```
2. Publish changes
3. Wait for config fetch
4. Check Android Logcat for:
   ```
   [CONFIG] 2024-01-15 14:00:01 | Excluded packages: com.android.systemui, com.whatsapp, com.example.testapp
   [NOTIFICATION-FILTERED] 2024-01-15 14:30:30 | com.whatsapp | WhatsApp | New message | EXCLUDED
   ```

## Advanced Configuration

### Conditional Values (Optional)
You can set different values based on conditions:

1. **By App Version**:
   - Condition: `app.version == '1.0'`
   - Value: Different settings for specific app versions

2. **By User Percentage**:
   - Condition: `percent <= 10`
   - Value: Test new settings with 10% of users

3. **By Device Type**:
   - Condition: `device.os == 'android'`
   - Value: Android-specific settings

### A/B Testing
1. Create parameter variants for testing
2. Set percentage splits
3. Monitor performance metrics
4. Gradually roll out successful configurations

## Monitoring and Debugging

### Key Log Messages to Monitor
```
[CONFIG] Remote Config initialized successfully
[CONFIG] Remote config fetched and activated successfully
[CONFIG] Location interval updated: X minutes
[CONFIG] Excluded packages: package1, package2, package3
[CONFIG_REFRESH] Config refresh completed successfully
[NOTIFICATION-FILTERED] packageName | appName | title | EXCLUDED
```

### Troubleshooting
1. **Config not updating**: Check network connectivity and fetch intervals
2. **Invalid JSON**: Validate JSON format for excluded packages
3. **Out of range values**: Location interval will be clamped to 5-120 minutes
4. **Fetch failures**: Check Firebase project configuration and API keys

## Best Practices

1. **Gradual Rollouts**: Test changes with small user percentages first
2. **Reasonable Defaults**: Always set sensible default values
3. **Validation**: App validates all config values and applies constraints
4. **Monitoring**: Monitor logs for config changes and errors
5. **Documentation**: Document all parameter changes with version descriptions

## Security Considerations

1. **Sensitive Data**: Never put sensitive information in Remote Config
2. **Package Names**: Only exclude packages you own or have permission to filter
3. **Rate Limits**: Respect Firebase Remote Config quotas and limits
4. **Validation**: App validates all incoming config values

## Support

For issues with Remote Config setup:
1. Check Firebase Console for error messages
2. Review Android Logcat for detailed error logs
3. Verify Firebase project permissions
4. Ensure app has network connectivity for config fetches
