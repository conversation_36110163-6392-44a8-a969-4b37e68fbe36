package com.ab.notif

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.location.Location
import android.util.Log
import androidx.core.app.ActivityCompat
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.ab.notif.data.LocationData
import com.ab.notif.firebase.FirebaseManager
import com.ab.notif.firebase.SyncResult
import com.google.android.gms.location.FusedLocationProviderClient
import com.google.android.gms.location.LocationServices
import com.google.android.gms.location.Priority
import java.text.SimpleDateFormat
import java.util.*
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 * WorkManager Worker that captures device location every 15 minutes,
 * logs it to Android Logcat, and syncs it to Firebase Firestore.
 * Phase 3: Added Firebase sync while keeping all existing logging functionality intact.
 */
class LocationWorker(
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {

    companion object {
        private const val TAG = "LOCATION_CAPTURE"
        private const val LOG_PREFIX = "[LOCATION]"
        private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        const val WORK_NAME = "location_capture_work"
    }

    private val fusedLocationClient: FusedLocationProviderClient by lazy {
        LocationServices.getFusedLocationProviderClient(applicationContext)
    }

    private val firebaseManager = FirebaseManager.getInstance()

    override suspend fun doWork(): Result {
        Log.d(TAG, "$LOG_PREFIX LocationWorker started")

        return try {
            if (!hasLocationPermissions()) {
                Log.w(TAG, "$LOG_PREFIX Location permissions not granted, skipping location capture")
                return Result.success()
            }

            val location = getCurrentLocation()
            if (location != null) {
                logAndSyncLocation(location)
                Log.d(TAG, "$LOG_PREFIX Location captured successfully")
            } else {
                Log.w(TAG, "$LOG_PREFIX Failed to get current location")
            }

            Result.success()
        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX Error in LocationWorker: ${e.message}", e)
            Result.failure()
        }
    }

    private fun hasLocationPermissions(): Boolean {
        val fineLocationGranted = ActivityCompat.checkSelfPermission(
            applicationContext,
            Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED

        val coarseLocationGranted = ActivityCompat.checkSelfPermission(
            applicationContext,
            Manifest.permission.ACCESS_COARSE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED

        return fineLocationGranted || coarseLocationGranted
    }

    private suspend fun getCurrentLocation(): Location? {
        return try {
            if (!hasLocationPermissions()) {
                Log.w(TAG, "$LOG_PREFIX Location permissions not available")
                return null
            }

            // Use suspendCoroutine to convert callback-based API to coroutine
            suspendCoroutine { continuation ->
                try {
                    fusedLocationClient.getCurrentLocation(
                        Priority.PRIORITY_BALANCED_POWER_ACCURACY,
                        null
                    ).addOnSuccessListener { location ->
                        Log.d(TAG, "$LOG_PREFIX Location request successful: $location")
                        continuation.resume(location)
                    }.addOnFailureListener { exception ->
                        Log.e(TAG, "$LOG_PREFIX Location request failed: ${exception.message}", exception)
                        continuation.resume(null)
                    }
                } catch (e: SecurityException) {
                    Log.e(TAG, "$LOG_PREFIX Security exception getting location: ${e.message}", e)
                    continuation.resume(null)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX Exception getting current location: ${e.message}", e)
            null
        }
    }

    private suspend fun logAndSyncLocation(location: Location) {
        val timestamp = getCurrentTimestamp()
        val latitude = String.format("%.6f", location.latitude)
        val longitude = String.format("%.6f", location.longitude)
        val accuracy = String.format("%.1f", location.accuracy)

        // PHASE 1 & 2: Keep existing logging functionality intact
        val baseLogMessage = "$LOG_PREFIX $timestamp | Lat: $latitude, Lng: $longitude | Accuracy: ${accuracy}m"

        // PHASE 3: Add Firebase sync and enhanced logging
        try {
            // Create location data for Firebase
            val locationData = LocationData.create(location, "")  // Device ID will be set by FirebaseManager

            // Sync to Firebase
            val syncResult = firebaseManager.syncLocation(applicationContext, locationData)

            // Enhanced logging with sync status
            val syncStatus = when (syncResult) {
                SyncResult.SYNCED -> "SYNCED"
                SyncResult.FAILED -> "SYNC_FAILED"
            }

            val enhancedLogMessage = "$baseLogMessage | $syncStatus"
            Log.i(TAG, enhancedLogMessage)

        } catch (e: Exception) {
            // If Firebase sync fails, still log the location (Phase 1 & 2 functionality preserved)
            Log.i(TAG, "$baseLogMessage | SYNC_ERROR")
            Log.e(TAG, "$LOG_PREFIX Firebase sync error: ${e.message}", e)
        }

        // Additional debug information
        Log.d(TAG, "$LOG_PREFIX DETAILS | Provider: ${location.provider} | " +
                "Time: ${Date(location.time)} | " +
                "Altitude: ${if (location.hasAltitude()) "${location.altitude}m" else "N/A"} | " +
                "Speed: ${if (location.hasSpeed()) "${location.speed}m/s" else "N/A"}")
    }

    private fun getCurrentTimestamp(): String {
        return dateFormat.format(Date())
    }
}
