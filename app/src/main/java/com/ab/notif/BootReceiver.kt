package com.ab.notif

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log

/**
 * BroadcastReceiver that automatically starts the NotificationCaptureService
 * when the device boots up, ensuring continuous notification monitoring.
 */
class BootReceiver : BroadcastReceiver() {

    companion object {
        private const val TAG = "NOTIFICATION_BOOT"
        private const val LOG_PREFIX = "[BOOT_RECEIVER]"
    }

    override fun onReceive(context: Context?, intent: Intent?) {
        if (context == null || intent == null) {
            Log.w(TAG, "$LOG_PREFIX Received null context or intent")
            return
        }

        when (intent.action) {
            Intent.ACTION_BOOT_COMPLETED -> {
                Log.i(TAG, "$LOG_PREFIX Device boot completed, attempting to start notification service")
                startNotificationService(context)
            }
            Intent.ACTION_MY_PACKAGE_REPLACED -> {
                Log.i(TAG, "$LOG_PREFIX App package replaced, attempting to restart notification service")
                startNotificationService(context)
            }
            Intent.ACTION_PACKAGE_REPLACED -> {
                if (intent.dataString?.contains(context.packageName) == true) {
                    Log.i(TAG, "$LOG_PREFIX App updated, attempting to restart notification service")
                    startNotificationService(context)
                }
            }
        }
    }

    private fun startNotificationService(context: Context) {
        try {
            // Note: NotificationListenerService cannot be started directly like a regular service
            // It needs to be enabled in device settings and will start automatically when enabled
            // This receiver mainly serves to log boot events and potentially perform other initialization
            
            Log.i(TAG, "$LOG_PREFIX Boot receiver triggered - NotificationListenerService should auto-start if enabled")
            
            // Optional: You could add logic here to check if the service is enabled
            // and potentially show a notification to remind user to enable it
            
        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX Error in boot receiver: ${e.message}", e)
        }
    }
}
