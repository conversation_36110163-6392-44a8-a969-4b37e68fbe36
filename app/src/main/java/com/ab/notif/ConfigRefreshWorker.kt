package com.ab.notif

import android.content.Context
import android.util.Log
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.ab.notif.firebase.RemoteConfigManager
import java.text.SimpleDateFormat
import java.util.*

/**
 * WorkManager Worker that periodically refreshes Firebase Remote Config.
 * Runs every 12 hours to check for configuration updates.
 */
class ConfigRefreshWorker(
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {

    companion object {
        private const val TAG = "CONFIG_REFRESH"
        private const val LOG_PREFIX = "[CONFIG_REFRESH]"
        private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
    }

    private val remoteConfigManager = RemoteConfigManager.getInstance()

    override suspend fun doWork(): Result {
        return try {
            val currentTime = dateFormat.format(Date())
            Log.d(TAG, "$LOG_PREFIX $currentTime | Starting config refresh check")

            // Check if refresh is needed
            if (!remoteConfigManager.shouldRefreshConfig()) {
                Log.d(TAG, "$LOG_PREFIX $currentTime | Config refresh not needed yet")
                return Result.success()
            }

            // Attempt to fetch and activate new config
            val success = remoteConfigManager.fetchAndActivate(applicationContext)
            
            if (success) {
                Log.i(TAG, "$LOG_PREFIX $currentTime | Config refresh completed successfully")
                Result.success()
            } else {
                Log.d(TAG, "$LOG_PREFIX $currentTime | Config refresh completed, no changes")
                Result.success()
            }

        } catch (e: Exception) {
            val currentTime = dateFormat.format(Date())
            Log.e(TAG, "$LOG_PREFIX $currentTime | Config refresh failed: ${e.message}", e)
            
            // Retry on failure
            Result.retry()
        }
    }
}
