package com.ab.notif

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Intent
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat

/**
 * Foreground service to maintain background location access.
 * This service ensures that location tracking can continue in the background
 * even when the app is not actively running.
 */
class LocationService : Service() {

    companion object {
        private const val TAG = "LOCATION_SERVICE"
        private const val LOG_PREFIX = "[LOCATION_SERVICE]"
        private const val NOTIFICATION_ID = 2001
        private const val CHANNEL_ID = "location_service_channel"
        private const val CHANNEL_NAME = "Location Tracking"
    }

    override fun onCreate() {
        super.onCreate()
        Log.i(TAG, "$LOG_PREFIX LocationService created")
        createNotificationChannel()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.i(TAG, "$LOG_PREFIX LocationService started")
        
        val notification = createNotification()
        startForeground(NOTIFICATION_ID, notification)
        
        return START_STICKY // Restart service if killed by system
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null // This is not a bound service
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.i(TAG, "$LOG_PREFIX LocationService destroyed")
    }

    private fun createNotificationChannel() {
        val channel = NotificationChannel(
            CHANNEL_ID,
            CHANNEL_NAME,
            NotificationManager.IMPORTANCE_LOW
        ).apply {
            description = "Notification channel for location tracking service"
            setShowBadge(false)
        }

        val notificationManager = getSystemService(NotificationManager::class.java)
        notificationManager.createNotificationChannel(channel)
        
        Log.d(TAG, "$LOG_PREFIX Notification channel created")
    }

    private fun createNotification(): Notification {
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("NotifSync Location Tracking")
            .setContentText("Capturing location every 15 minutes")
            .setSmallIcon(android.R.drawable.ic_menu_mylocation)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setOngoing(true)
            .setAutoCancel(false)
            .build()
    }
}
