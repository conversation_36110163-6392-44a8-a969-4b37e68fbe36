package com.ab.notif.firebase

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.ab.notif.NotifSyncApplication
import com.google.firebase.FirebaseApp
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.google.firebase.remoteconfig.FirebaseRemoteConfigSettings
import kotlinx.coroutines.tasks.await
import org.json.JSONArray
import org.json.JSONException
import java.text.SimpleDateFormat
import java.util.*
import kotlin.math.max
import kotlin.math.min

/**
 * Firebase Remote Config manager for dynamic app configuration.
 * Handles fetching, caching, and applying configuration changes for:
 * - Location interval updates
 * - Notification package filtering
 * - Config refresh scheduling
 */
class RemoteConfigManager private constructor() {
    
    companion object {
        private const val TAG = "REMOTE_CONFIG"
        private const val LOG_PREFIX = "[CONFIG]"
        
        // Config parameter keys
        const val KEY_LOCATION_INTERVAL_MINUTES = "location_interval_minutes"
        const val KEY_EXCLUDED_NOTIFICATION_PACKAGES = "excluded_notification_packages"
        
        // Default values
        const val DEFAULT_LOCATION_INTERVAL_MINUTES = 15L
        private val DEFAULT_EXCLUDED_PACKAGES = emptyList<String>()
        
        // Constraints
        private const val MIN_LOCATION_INTERVAL_MINUTES = 5L
        private const val MAX_LOCATION_INTERVAL_MINUTES = 120L
        
        // Cache preferences
        private const val PREFS_NAME = "remote_config_cache"
        private const val PREF_LAST_FETCH_TIME = "last_fetch_time"
        private const val PREF_LOCATION_INTERVAL = "location_interval_minutes"
        private const val PREF_EXCLUDED_PACKAGES = "excluded_packages"
        
        @Volatile
        private var INSTANCE: RemoteConfigManager? = null

        fun getInstance(): RemoteConfigManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: RemoteConfigManager().also { INSTANCE = it }
            }
        }
        
        private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
    }
    
    private lateinit var remoteConfig: FirebaseRemoteConfig
    private lateinit var sharedPrefs: SharedPreferences
    private var isInitialized = false
    
    // Config change listeners
    private val locationIntervalListeners = mutableListOf<(Long) -> Unit>()
    private val packageFilterListeners = mutableListOf<(List<String>) -> Unit>()
    
    /**
     * Initializes Firebase Remote Config with default values and settings
     */
    suspend fun initialize(context: Context): Boolean {
        return try {
            if (isInitialized) {
                Log.d(TAG, "$LOG_PREFIX Remote Config already initialized")
                return true
            }
            
            sharedPrefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            remoteConfig = FirebaseRemoteConfig.getInstance()
            
            // Set default values
            val defaults = mapOf(
                KEY_LOCATION_INTERVAL_MINUTES to DEFAULT_LOCATION_INTERVAL_MINUTES,
                KEY_EXCLUDED_NOTIFICATION_PACKAGES to "[]" // Empty JSON array
            )
            remoteConfig.setDefaultsAsync(defaults).await()
            
            // Configure Remote Config settings
            val configSettings = FirebaseRemoteConfigSettings.Builder()
                .setMinimumFetchIntervalInSeconds(3600) // 1 hour minimum fetch interval
                .build()
            remoteConfig.setConfigSettingsAsync(configSettings).await()
            
            isInitialized = true
            Log.i(TAG, "$LOG_PREFIX Remote Config initialized successfully")
            
            // Load cached values on initialization
            loadCachedValues()
            
            true
        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX Failed to initialize Remote Config: ${e.message}", e)
            false
        }
    }
    
    /**
     * Fetches latest config from Firebase and applies changes
     */
    suspend fun fetchAndActivate(context: Context): Boolean {
        return try {
            if (!isInitialized) {
                Log.w(TAG, "$LOG_PREFIX Remote Config not initialized, attempting to initialize...")
                if (!initialize(context)) {
                    return false
                }
            }
            
            Log.d(TAG, "$LOG_PREFIX Fetching remote config...")
            
            // Fetch and activate config
            val fetchResult = remoteConfig.fetchAndActivate().await()
            
            if (fetchResult) {
                Log.i(TAG, "$LOG_PREFIX Remote config fetched and activated successfully")
                
                // Apply new configuration
                applyConfiguration()
                
                // Cache the fetch time
                sharedPrefs.edit()
                    .putLong(PREF_LAST_FETCH_TIME, System.currentTimeMillis())
                    .apply()
                
                true
            } else {
                Log.d(TAG, "$LOG_PREFIX Remote config fetch completed, but no new config available")
                false
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX Failed to fetch remote config: ${e.message}", e)
            false
        }
    }
    
    /**
     * Gets the current location interval in minutes with validation
     */
    fun getLocationIntervalMinutes(): Long {
        return if (isInitialized) {
            val value = remoteConfig.getLong(KEY_LOCATION_INTERVAL_MINUTES)
            // Apply constraints
            min(max(value, MIN_LOCATION_INTERVAL_MINUTES), MAX_LOCATION_INTERVAL_MINUTES)
        } else {
            // Return cached value or default
            sharedPrefs.getLong(PREF_LOCATION_INTERVAL, DEFAULT_LOCATION_INTERVAL_MINUTES)
        }
    }
    
    /**
     * Gets the list of excluded notification packages
     */
    fun getExcludedNotificationPackages(): List<String> {
        return if (isInitialized) {
            try {
                val jsonString = remoteConfig.getString(KEY_EXCLUDED_NOTIFICATION_PACKAGES)
                parsePackageList(jsonString)
            } catch (e: Exception) {
                Log.w(TAG, "$LOG_PREFIX Error parsing excluded packages, using default: ${e.message}")
                DEFAULT_EXCLUDED_PACKAGES
            }
        } else {
            // Return cached value or default
            val cachedPackages = sharedPrefs.getString(PREF_EXCLUDED_PACKAGES, "[]") ?: "[]"
            parsePackageList(cachedPackages)
        }
    }
    
    /**
     * Registers a listener for location interval changes
     */
    fun addLocationIntervalListener(listener: (Long) -> Unit) {
        locationIntervalListeners.add(listener)
    }
    
    /**
     * Registers a listener for package filter changes
     */
    fun addPackageFilterListener(listener: (List<String>) -> Unit) {
        packageFilterListeners.add(listener)
    }
    
    /**
     * Checks if config should be refreshed (every 12 hours)
     */
    fun shouldRefreshConfig(): Boolean {
        val lastFetchTime = sharedPrefs.getLong(PREF_LAST_FETCH_TIME, 0)
        val currentTime = System.currentTimeMillis()
        val twelveHoursInMillis = 12 * 60 * 60 * 1000L // 12 hours
        
        return (currentTime - lastFetchTime) >= twelveHoursInMillis
    }
    
    private fun applyConfiguration() {
        val currentTime = getCurrentTimestamp()
        
        // Get new values
        val newLocationInterval = getLocationIntervalMinutes()
        val newExcludedPackages = getExcludedNotificationPackages()
        
        // Check if location interval changed
        val cachedLocationInterval = sharedPrefs.getLong(PREF_LOCATION_INTERVAL, DEFAULT_LOCATION_INTERVAL_MINUTES)
        if (newLocationInterval != cachedLocationInterval) {
            Log.i(TAG, "$LOG_PREFIX $currentTime | Location interval updated: $newLocationInterval minutes")
            
            // Cache new value
            sharedPrefs.edit().putLong(PREF_LOCATION_INTERVAL, newLocationInterval).apply()
            
            // Notify listeners
            locationIntervalListeners.forEach { it(newLocationInterval) }
        }
        
        // Check if excluded packages changed
        val cachedPackages = sharedPrefs.getString(PREF_EXCLUDED_PACKAGES, "[]") ?: "[]"
        val cachedPackageList = parsePackageList(cachedPackages)
        
        if (newExcludedPackages != cachedPackageList) {
            val packagesString = if (newExcludedPackages.isEmpty()) {
                "none"
            } else {
                newExcludedPackages.joinToString(", ")
            }
            Log.i(TAG, "$LOG_PREFIX $currentTime | Excluded packages: $packagesString")
            
            // Cache new value
            val jsonString = JSONArray(newExcludedPackages).toString()
            sharedPrefs.edit().putString(PREF_EXCLUDED_PACKAGES, jsonString).apply()
            
            // Notify listeners
            packageFilterListeners.forEach { it(newExcludedPackages) }
        }
    }
    
    private fun loadCachedValues() {
        // Load cached values and notify listeners
        val cachedLocationInterval = sharedPrefs.getLong(PREF_LOCATION_INTERVAL, DEFAULT_LOCATION_INTERVAL_MINUTES)
        val cachedPackages = sharedPrefs.getString(PREF_EXCLUDED_PACKAGES, "[]") ?: "[]"
        val cachedPackageList = parsePackageList(cachedPackages)
        
        Log.d(TAG, "$LOG_PREFIX Loaded cached config - Location interval: ${cachedLocationInterval}min, Excluded packages: ${cachedPackageList.size}")
        
        // Notify listeners with cached values
        locationIntervalListeners.forEach { it(cachedLocationInterval) }
        packageFilterListeners.forEach { it(cachedPackageList) }
    }
    
    private fun parsePackageList(jsonString: String): List<String> {
        return try {
            val jsonArray = JSONArray(jsonString)
            val packages = mutableListOf<String>()
            for (i in 0 until jsonArray.length()) {
                packages.add(jsonArray.getString(i))
            }
            packages
        } catch (e: JSONException) {
            Log.w(TAG, "$LOG_PREFIX Error parsing package list JSON: ${e.message}")
            DEFAULT_EXCLUDED_PACKAGES
        }
    }
    
    private fun getCurrentTimestamp(): String {
        return dateFormat.format(Date())
    }
}
