package com.ab.notif.firebase

import android.content.Context
import android.util.Log
import com.ab.notif.data.DeviceInfo
import com.ab.notif.data.LocationData
import com.ab.notif.data.NotificationData
import com.ab.notif.utils.DeviceIdManager
import com.ab.notif.utils.DeviceInfoManager
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.FirebaseFirestoreSettings
import kotlinx.coroutines.tasks.await
import java.text.SimpleDateFormat
import java.util.*

/**
 * Firebase integration manager for syncing notifications and locations to Firestore.
 * Handles authentication, offline sync, and error handling.
 */
class FirebaseManager private constructor() {
    
    companion object {
        private const val TAG = "FIREBASE_MANAGER"
        private const val LOG_PREFIX = "[FIREBASE]"
        private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        
        // Firestore collection paths
        private const val COLLECTION_DEVICES = "devices"
        private const val COLLECTION_NOTIFICATIONS = "notifications"
        private const val COLLECTION_LOCATIONS = "locations"
        private const val COLLECTION_DEVICE_INFO = "device_info"
        
        @Volatile
        private var INSTANCE: FirebaseManager? = null
        
        fun getInstance(): FirebaseManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: FirebaseManager().also { INSTANCE = it }
            }
        }
    }
    
    private val auth: FirebaseAuth = FirebaseAuth.getInstance()
    private val firestore: FirebaseFirestore = FirebaseFirestore.getInstance()
    private var isInitialized = false
    
    /**
     * Initializes Firebase with offline persistence and anonymous authentication
     */
    suspend fun initialize(context: Context): Boolean {
        return try {
            if (isInitialized) {
                Log.d(TAG, "$LOG_PREFIX Firebase already initialized")
                return true
            }
            
            // Enable offline persistence
            val settings = FirebaseFirestoreSettings.Builder()
                .setPersistenceEnabled(true)
                .build()
            firestore.firestoreSettings = settings
            
            // Authenticate anonymously
            if (auth.currentUser == null) {
                Log.d(TAG, "$LOG_PREFIX Authenticating anonymously...")
                auth.signInAnonymously().await()
                Log.i(TAG, "$LOG_PREFIX Anonymous authentication successful")
            } else {
                Log.d(TAG, "$LOG_PREFIX Already authenticated: ${auth.currentUser?.uid}")
            }
            
            isInitialized = true
            Log.i(TAG, "$LOG_PREFIX Firebase initialized successfully")
            true
            
        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX Failed to initialize Firebase: ${e.message}", e)
            false
        }
    }
    
    /**
     * Syncs notification data to Firestore
     */
    suspend fun syncNotification(context: Context, notificationData: NotificationData): SyncResult {
        return try {
            if (!isInitialized) {
                Log.w(TAG, "$LOG_PREFIX Firebase not initialized, attempting to initialize...")
                if (!initialize(context)) {
                    return SyncResult.FAILED
                }
            }
            
            val deviceId = DeviceIdManager.getDeviceId(context)
            val dataWithDeviceId = notificationData.copy(deviceId = deviceId)
            
            firestore
                .collection(COLLECTION_DEVICES)
                .document(deviceId)
                .collection(COLLECTION_NOTIFICATIONS)
                .add(dataWithDeviceId)
                .await()
            
            Log.d(TAG, "$LOG_PREFIX Notification synced successfully for device: $deviceId")
            SyncResult.SYNCED
            
        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX Failed to sync notification: ${e.message}", e)
            SyncResult.FAILED
        }
    }
    
    /**
     * Syncs location data to Firestore
     */
    suspend fun syncLocation(context: Context, locationData: LocationData): SyncResult {
        return try {
            if (!isInitialized) {
                Log.w(TAG, "$LOG_PREFIX Firebase not initialized, attempting to initialize...")
                if (!initialize(context)) {
                    return SyncResult.FAILED
                }
            }
            
            val deviceId = DeviceIdManager.getDeviceId(context)
            val dataWithDeviceId = locationData.copy(deviceId = deviceId)
            
            firestore
                .collection(COLLECTION_DEVICES)
                .document(deviceId)
                .collection(COLLECTION_LOCATIONS)
                .add(dataWithDeviceId)
                .await()
            
            Log.d(TAG, "$LOG_PREFIX Location synced successfully for device: $deviceId")
            SyncResult.SYNCED
            
        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX Failed to sync location: ${e.message}", e)
            SyncResult.FAILED
        }
    }
    
    /**
     * Checks if Firebase is properly initialized and connected
     */
    fun isReady(): Boolean {
        return isInitialized && auth.currentUser != null
    }
    
    /**
     * Gets the current device ID
     */
    fun getDeviceId(context: Context): String {
        return DeviceIdManager.getDeviceId(context)
    }

    /**
     * Registers device information on first app startup
     * Phase 6: Device information storage
     */
    suspend fun registerDevice(context: Context): SyncResult {
        return try {
            if (!isInitialized) {
                Log.w(TAG, "$LOG_PREFIX Firebase not initialized, attempting to initialize...")
                if (!initialize(context)) {
                    return SyncResult.FAILED
                }
            }

            val deviceId = DeviceIdManager.getDeviceId(context)

            // Check if device info already exists
            val existingInfo = getStoredDeviceInfo(context)
            if (existingInfo != null) {
                Log.d(TAG, "$LOG_PREFIX Device already registered, checking for updates...")
                return updateDeviceInfoIfChanged(context)
            }

            // Collect fresh device information
            val deviceInfo = DeviceInfoManager.collectDeviceInfo(context)
            if (deviceInfo == null) {
                Log.e(TAG, "$LOG_PREFIX Failed to collect device information")
                return SyncResult.FAILED
            }

            // Mark as first time registration
            val firstTimeInfo = deviceInfo.copy(firstSeen = com.google.firebase.Timestamp.now())

            // Store device info in Firebase
            firestore
                .collection(COLLECTION_DEVICES)
                .document(deviceId)
                .collection(COLLECTION_DEVICE_INFO)
                .document("info")
                .set(firstTimeInfo)
                .await()

            val currentTime = dateFormat.format(Date())
            Log.i(TAG, "[DEVICE-INFO] $currentTime | Device registered | $deviceId")
            DeviceInfoManager.logDeviceInfo(firstTimeInfo)

            SyncResult.SYNCED

        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX Failed to register device: ${e.message}", e)
            SyncResult.FAILED
        }
    }

    /**
     * Updates FCM token in device info
     * Phase 6: FCM token management
     */
    suspend fun updateFCMToken(context: Context, newToken: String): SyncResult {
        return try {
            if (!isInitialized) {
                Log.w(TAG, "$LOG_PREFIX Firebase not initialized, attempting to initialize...")
                if (!initialize(context)) {
                    return SyncResult.FAILED
                }
            }

            val deviceId = DeviceIdManager.getDeviceId(context)

            // Update only FCM token and last_updated timestamp
            val updateData = mapOf(
                "fcmToken" to newToken,
                "lastUpdated" to com.google.firebase.Timestamp.now()
            )

            firestore
                .collection(COLLECTION_DEVICES)
                .document(deviceId)
                .collection(COLLECTION_DEVICE_INFO)
                .document("info")
                .update(updateData)
                .await()

            val currentTime = dateFormat.format(Date())
            Log.i(TAG, "[DEVICE-INFO] $currentTime | FCM Token: ${newToken.take(20)}... | STORED")

            SyncResult.SYNCED

        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX Failed to update FCM token: ${e.message}", e)
            SyncResult.FAILED
        }
    }

    /**
     * Updates device information if changes are detected
     * Phase 6: Device info verification and updates
     */
    suspend fun updateDeviceInfoIfChanged(context: Context): SyncResult {
        return try {
            if (!isInitialized) {
                Log.w(TAG, "$LOG_PREFIX Firebase not initialized, attempting to initialize...")
                if (!initialize(context)) {
                    return SyncResult.FAILED
                }
            }

            val storedInfo = getStoredDeviceInfo(context)
            val updatedInfo = DeviceInfoManager.detectChanges(context, storedInfo)

            if (updatedInfo == null) {
                val currentTime = dateFormat.format(Date())
                Log.i(TAG, "[DEVICE-INFO] $currentTime | Daily check | No changes detected")
                return SyncResult.SYNCED
            }

            val deviceId = DeviceIdManager.getDeviceId(context)

            // Update device info in Firebase
            firestore
                .collection(COLLECTION_DEVICES)
                .document(deviceId)
                .collection(COLLECTION_DEVICE_INFO)
                .document("info")
                .set(updatedInfo)
                .await()

            Log.i(TAG, "$LOG_PREFIX Device info updated for device: $deviceId")

            SyncResult.SYNCED

        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX Failed to update device info: ${e.message}", e)
            SyncResult.FAILED
        }
    }

    /**
     * Retrieves stored device information from Firebase
     */
    private suspend fun getStoredDeviceInfo(context: Context): DeviceInfo? {
        return try {
            val deviceId = DeviceIdManager.getDeviceId(context)

            val document = firestore
                .collection(COLLECTION_DEVICES)
                .document(deviceId)
                .collection(COLLECTION_DEVICE_INFO)
                .document("info")
                .get()
                .await()

            if (document.exists()) {
                document.toObject(DeviceInfo::class.java)
            } else {
                null
            }

        } catch (e: Exception) {
            Log.w(TAG, "$LOG_PREFIX Failed to retrieve stored device info: ${e.message}")
            null
        }
    }
}

/**
 * Enum representing sync operation results
 */
enum class SyncResult {
    SYNCED,
    FAILED
}
