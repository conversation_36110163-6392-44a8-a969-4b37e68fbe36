package com.ab.notif

import android.app.Application
import android.content.Intent
import android.util.Log
import androidx.work.*
import com.ab.notif.firebase.FirebaseManager
import com.ab.notif.firebase.RemoteConfigManager
import com.google.firebase.FirebaseApp
import com.google.firebase.messaging.FirebaseMessaging
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.concurrent.TimeUnit

/**
 * Application class that initializes WorkManager and starts periodic location tracking.
 * This ensures that location capture begins as soon as the app is installed and runs.
 * Phase 4: Added Firebase Remote Config for dynamic location intervals and notification filtering.
 * Phase 5: Added Firebase Cloud Messaging for instant location requests.
 */
class NotifSyncApplication : Application() {

    companion object {
        private const val TAG = "NOTIF_SYNC_APP"
        private const val LOG_PREFIX = "[APPLICATION]"
    }

    private val remoteConfigManager: RemoteConfigManager by lazy {RemoteConfigManager.getInstance()}
    private val firebaseManager: FirebaseManager by lazy{FirebaseManager.getInstance()}
    private val applicationScope = CoroutineScope(Dispatchers.IO)

    override fun onCreate() {
        super.onCreate()
        Log.i(TAG, "$LOG_PREFIX NotifSyncApplication started - Phase 6 with Device Registry")

        // Initialize Firebase synchronously first
        try {
            FirebaseApp.initializeApp(this)
            Log.d(TAG, "$LOG_PREFIX Firebase initialized successfully")
        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX Failed to initialize Firebase: ${e.message}", e)
        }

        // Initialize Firebase-dependent services in background
        applicationScope.launch {
            // Initialize Firebase manager
            val firebaseInitialized = firebaseManager.initialize(this@NotifSyncApplication)
            if (firebaseInitialized) {
                // Only proceed with Firebase-dependent operations if initialization succeeded
                initializeDeviceRegistry()
                initializeRemoteConfig()
                initializeFCM()
                scheduleDailyDeviceVerification()
            } else {
                Log.e(TAG, "$LOG_PREFIX Firebase manager initialization failed, skipping Firebase-dependent operations")
            }
        }

        // Initialize non-Firebase dependent services immediately
        initializeLocationTracking()
        startLocationService()
    }

    private fun initializeLocationTracking() {
        try {
            // Get initial location interval from Remote Config (or default)
            val locationIntervalMinutes = remoteConfigManager.getLocationIntervalMinutes()

            scheduleLocationWork(locationIntervalMinutes)

            // Register listener for location interval changes
            remoteConfigManager.addLocationIntervalListener { newInterval ->
                Log.i(TAG, "$LOG_PREFIX Location interval changed to $newInterval minutes, rescheduling work")
                scheduleLocationWork(newInterval)
            }

        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX Error initializing location tracking: ${e.message}", e)
        }
    }

    private fun scheduleLocationWork(intervalMinutes: Long) {
        try {
            // Create constraints for the work
            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
                .setRequiresBatteryNotLow(false)
                .setRequiresCharging(false)
                .setRequiresDeviceIdle(false)
                .setRequiresStorageNotLow(false)
                .build()

            // Create periodic work request with dynamic interval
            val locationWorkRequest = PeriodicWorkRequestBuilder<LocationWorker>(
                intervalMinutes, TimeUnit.MINUTES,
                5, TimeUnit.MINUTES // Flex interval for battery optimization
            )
                .setConstraints(constraints)
                .setBackoffCriteria(
                    BackoffPolicy.LINEAR,
                    WorkRequest.MIN_BACKOFF_MILLIS,
                    TimeUnit.MILLISECONDS
                )
                .build()

            // Enqueue the work with replace policy to update interval
            WorkManager.getInstance(this).enqueueUniquePeriodicWork(
                LocationWorker.WORK_NAME,
                ExistingPeriodicWorkPolicy.REPLACE, // Replace existing work to update interval
                locationWorkRequest
            )

            Log.i(TAG, "$LOG_PREFIX Location tracking WorkManager scheduled - capturing every $intervalMinutes minutes")

        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX Error scheduling location work: ${e.message}", e)
        }
    }

    private fun startLocationService() {
        try {
            // Start the foreground service for background location access
            val serviceIntent = Intent(this, LocationService::class.java)
            startForegroundService(serviceIntent)
            
            Log.i(TAG, "$LOG_PREFIX LocationService started")
        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX Error starting LocationService: ${e.message}", e)
        }
    }

    private suspend fun initializeRemoteConfig() {
        try {
            Log.d(TAG, "$LOG_PREFIX Initializing Remote Config...")

            // Initialize Remote Config
            val initialized = remoteConfigManager.initialize(this)
            if (!initialized) {
                Log.w(TAG, "$LOG_PREFIX Remote Config initialization failed, using default values")
                return
            }

            // Fetch initial config
            val fetched = remoteConfigManager.fetchAndActivate(this)
            if (fetched) {
                Log.i(TAG, "$LOG_PREFIX Initial Remote Config fetch successful")
            } else {
                Log.d(TAG, "$LOG_PREFIX Using cached or default Remote Config values")
            }

            // Schedule periodic config refresh every 12 hours
            scheduleConfigRefresh()

        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX Error initializing Remote Config: ${e.message}", e)
        }
    }

    private fun scheduleConfigRefresh() {
        try {
            // Create constraints for config refresh work
            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED) // Need network for config fetch
                .setRequiresBatteryNotLow(false)
                .setRequiresCharging(false)
                .setRequiresDeviceIdle(false)
                .setRequiresStorageNotLow(false)
                .build()

            // Create periodic work request for config refresh every 12 hours
            val configRefreshRequest = PeriodicWorkRequestBuilder<ConfigRefreshWorker>(
                12, TimeUnit.HOURS,
                1, TimeUnit.HOURS // Flex interval
            )
                .setConstraints(constraints)
                .setBackoffCriteria(
                    BackoffPolicy.EXPONENTIAL,
                    WorkRequest.MIN_BACKOFF_MILLIS,
                    TimeUnit.MILLISECONDS
                )
                .build()

            // Enqueue the work
            WorkManager.getInstance(this).enqueueUniquePeriodicWork(
                "config_refresh_work",
                ExistingPeriodicWorkPolicy.KEEP,
                configRefreshRequest
            )

            Log.i(TAG, "$LOG_PREFIX Config refresh scheduled - checking every 12 hours")

        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX Error scheduling config refresh: ${e.message}", e)
        }
    }

    /**
     * Initializes Firebase Cloud Messaging and logs the FCM token for testing.
     * Phase 5: FCM token management for instant location requests.
     */
    private suspend fun initializeFCM() {
        try {
            Log.d(TAG, "$LOG_PREFIX Initializing Firebase Cloud Messaging...")

            // Get FCM token for testing purposes
            FirebaseMessaging.getInstance().token.addOnCompleteListener { task ->
                if (!task.isSuccessful) {
                    Log.w(TAG, "$LOG_PREFIX Failed to get FCM token: ${task.exception?.message}")
                    return@addOnCompleteListener
                }

                // Get new FCM registration token
                val token = task.result
                Log.i(TAG, "$LOG_PREFIX FCM initialization successful")
                Log.i(TAG, "$LOG_PREFIX FCM Token for testing: $token")
                Log.i(TAG, "$LOG_PREFIX Use this token to send test messages from Firebase Console")
            }

        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX Error initializing FCM: ${e.message}", e)
        }
    }

    /**
     * Initializes device registry by registering device information in Firebase.
     * Phase 6: Device information storage on app startup.
     */
    private suspend fun initializeDeviceRegistry() {
        try {
            Log.d(TAG, "$LOG_PREFIX Initializing device registry...")

            val syncResult = firebaseManager.registerDevice(this@NotifSyncApplication)

            when (syncResult) {
                com.ab.notif.firebase.SyncResult.SYNCED -> {
                    Log.i(TAG, "$LOG_PREFIX Device registry initialization successful")
                }
                com.ab.notif.firebase.SyncResult.FAILED -> {
                    Log.w(TAG, "$LOG_PREFIX Device registry initialization failed")
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX Error initializing device registry: ${e.message}", e)
        }
    }

    /**
     * Schedules daily device information verification.
     * Phase 6: Daily device info verification and updates.
     */
    private fun scheduleDailyDeviceVerification() {
        try {
            Log.d(TAG, "$LOG_PREFIX Scheduling daily device verification...")

            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED)
                .setRequiresBatteryNotLow(false)
                .setRequiresCharging(false)
                .setRequiresDeviceIdle(false)
                .setRequiresStorageNotLow(false)
                .build()

            val deviceVerificationRequest = PeriodicWorkRequestBuilder<DeviceVerificationWorker>(
                24, TimeUnit.HOURS, // Run once per day
                2, TimeUnit.HOURS   // Flex interval
            )
                .setConstraints(constraints)
                .setBackoffCriteria(
                    BackoffPolicy.LINEAR,
                    WorkRequest.MIN_BACKOFF_MILLIS,
                    TimeUnit.MILLISECONDS
                )
                .build()

            WorkManager.getInstance(this)
                .enqueueUniquePeriodicWork(
                    "device_verification",
                    ExistingPeriodicWorkPolicy.KEEP,
                    deviceVerificationRequest
                )

            Log.i(TAG, "$LOG_PREFIX Daily device verification scheduled successfully")

        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX Error scheduling device verification: ${e.message}", e)
        }
    }
}
