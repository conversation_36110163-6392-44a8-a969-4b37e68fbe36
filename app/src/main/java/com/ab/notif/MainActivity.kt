package com.ab.notif

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.util.Log
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.app.NotificationManagerCompat
import androidx.core.content.ContextCompat

//import androidx.core.content.ContextCompat

/**
 * Minimal MainActivity for the headless notification and location capture app.
 * This activity provides setup instructions for enabling notification access and location permissions,
 * then finishes itself to keep the app truly headless.
 */
class MainActivity : ComponentActivity() {

    companion object {
        private const val TAG = "NOTIF_SYNC_MAIN"
        private const val LOG_PREFIX = "[MAIN_ACTIVITY]"
    }

    private val locationPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val fineLocationGranted = permissions[Manifest.permission.ACCESS_FINE_LOCATION] ?: false
        val coarseLocationGranted = permissions[Manifest.permission.ACCESS_COARSE_LOCATION] ?: false

        if (fineLocationGranted || coarseLocationGranted) {
            Log.i(TAG, "$LOG_PREFIX Location permissions granted")
            requestBackgroundLocationPermission()
        } else {
            Log.w(TAG, "$LOG_PREFIX Location permissions denied")
            Toast.makeText(this, "Location permissions are required for location tracking", Toast.LENGTH_LONG).show()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        Log.i(TAG, "$LOG_PREFIX MainActivity started - Phase 2 with location tracking")

        // Show setup instructions
        showSetupInstructions()

        // Request location permissions first
        val isPermissionGranted = requestLocationPermissions()
        if(!isPermissionGranted) return

        // Open notification access settings
        if(!isNotificationAccessGranted(this)){
            openNotificationSettings()
        }else{
            openSecuritySettings()
        }

        // Finish the activity to keep the app headless
        finish()
        Log.i(TAG, "$LOG_PREFIX MainActivity finished - app is now headless")
    }

    private fun showSetupInstructions() {
        Toast.makeText(
            this,
            "Please Enable notification access AND location permissions for full functionality",
            Toast.LENGTH_LONG
        ).show()
    }

    private fun requestLocationPermissions(): Boolean {
        val permissionsToRequest = mutableListOf<String>()

        if (ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION)
            != PackageManager.PERMISSION_GRANTED) {
            permissionsToRequest.add(Manifest.permission.ACCESS_FINE_LOCATION)
        }

        if (ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_COARSE_LOCATION)
            != PackageManager.PERMISSION_GRANTED) {
            permissionsToRequest.add(Manifest.permission.ACCESS_COARSE_LOCATION)
        }

        if (permissionsToRequest.isNotEmpty()) {
            Log.i(TAG, "$LOG_PREFIX Requesting location permissions: $permissionsToRequest")
            locationPermissionLauncher.launch(permissionsToRequest.toTypedArray())
        } else {
            Log.i(TAG, "$LOG_PREFIX Location permissions already granted")
            requestBackgroundLocationPermission()
            return true
        }
        return false
    }

    private fun requestBackgroundLocationPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_BACKGROUND_LOCATION)
                != PackageManager.PERMISSION_GRANTED) {

                Log.i(TAG, "$LOG_PREFIX Opening app settings for background location permission")
                Toast.makeText(
                    this,
                    "Please enable 'Allow all the time' for location access in app settings",
                    Toast.LENGTH_LONG
                ).show()

                try {
                    val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                        data = Uri.fromParts("package", packageName, null)
                        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    }
                    startActivity(intent)
                } catch (e: Exception) {
                    Log.e(TAG, "$LOG_PREFIX Failed to open app settings: ${e.message}")
                }
            }
        }
    }

    private fun openNotificationSettings() {
        try {
            val intent = Intent(Settings.ACTION_NOTIFICATION_LISTENER_SETTINGS)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            startActivity(intent)
            Log.i(TAG, "$LOG_PREFIX Opened notification access settings")
        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX Failed to open notification access settings: ${e.message}")
            Toast.makeText(this, "Please manually enable notification access in Settings", Toast.LENGTH_LONG).show()
        }
    }

    private fun openSecuritySettings() {
        try {
            val intent = Intent(Settings.ACTION_SECURITY_SETTINGS)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            startActivity(intent)
            Log.i(TAG, "$LOG_PREFIX Opened notification access settings")
        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX Failed to open notification access settings: ${e.message}")
            Toast.makeText(this, "Please manually enable notification access in Settings", Toast.LENGTH_LONG).show()
        }
    }

    private fun isNotificationAccessGranted(context: Context): Boolean {
        return NotificationManagerCompat.getEnabledListenerPackages(context)
            .contains(context.packageName)
    }
}