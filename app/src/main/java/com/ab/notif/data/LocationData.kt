package com.ab.notif.data

import android.location.Location

/**
 * Data class representing a location for Firebase Firestore storage.
 * Matches the Firestore structure specified in Phase 3 requirements.
 */
data class LocationData(
    val timestamp: Long = 0L,
    val latitude: Double = 0.0,
    val longitude: Double = 0.0,
    val accuracy: Float = 0.0f,
    val deviceId: String = ""
) {
    // No-argument constructor required by Firestore
    constructor() : this(0L, 0.0, 0.0, 0.0f, "")
    
    companion object {
        /**
         * Creates LocationData from Android Location object
         */
        fun create(location: Location, deviceId: String): LocationData {
            return LocationData(
                timestamp = System.currentTimeMillis(),
                latitude = location.latitude,
                longitude = location.longitude,
                accuracy = location.accuracy,
                deviceId = deviceId
            )
        }
    }
}
