package com.ab.notif.data

/**
 * Data class representing a notification for Firebase Firestore storage.
 * Matches the Firestore structure specified in Phase 3 requirements.
 */
data class NotificationData(
    val timestamp: Long = 0L,
    val packageName: String = "",
    val appName: String = "",
    val title: String = "",
    val content: String = "",
    val deviceId: String = ""
) {
    // No-argument constructor required by Firestore
    constructor() : this(0L, "", "", "", "", "")
    
    companion object {
        /**
         * Creates NotificationData from notification capture parameters
         */
        fun create(
            packageName: String,
            appName: String,
            title: String,
            content: String,
            deviceId: String
        ): NotificationData {
            return NotificationData(
                timestamp = System.currentTimeMillis(),
                packageName = packageName,
                appName = appName,
                title = title,
                content = content,
                deviceId = deviceId
            )
        }
    }
}
