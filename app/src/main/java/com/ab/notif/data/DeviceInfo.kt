package com.ab.notif.data

import com.google.firebase.Timestamp

/**
 * Data class representing device information for Firebase Firestore storage.
 * Matches the Firebase structure specified in Phase 6 requirements.
 *
 * Firebase Structure:
 * devices/[DEVICE_ID]/device_info/
 */
data class DeviceInfo(
    val deviceModel: String = "",        // Build.MODEL
    val deviceBrand: String = "",        // Build.MANUFACTURER
    val deviceName: String = "",         // Build.DEVICE
    val softwareId: String = "",         // Android ID / Enrollment ID
    val androidVersion: String = "",     // Build.VERSION.RELEASE
    val apiLevel: Int = 0,              // Build.VERSION.SDK_INT
    val fcmToken: String = "",          // Current FCM registration token
    val appVersion: String = "",        // App version name
    val firstSeen: Timestamp? = null,   // When device was first registered
    val lastUpdated: Timestamp? = null, // Last time info was updated
    val deviceId: String = ""           // Same as parent collection key
) {
    // No-argument constructor required by Firestore
    constructor() : this("", "", "", "", "", 0, "", "", null, null, "")

    companion object {
        /**
         * Creates DeviceInfo from device information parameters
         */
        fun create(
            deviceModel: String,
            deviceBrand: String,
            deviceName: String,
            softwareId: String,
            androidVersion: String,
            apiLevel: Int,
            fcmToken: String,
            appVersion: String,
            deviceId: String,
            isFirstTime: Boolean = false
        ): DeviceInfo {
            val currentTime = Timestamp.now()
            return DeviceInfo(
                deviceModel = deviceModel,
                deviceBrand = deviceBrand,
                deviceName = deviceName,
                softwareId = softwareId,
                androidVersion = androidVersion,
                apiLevel = apiLevel,
                fcmToken = fcmToken,
                appVersion = appVersion,
                firstSeen = if (isFirstTime) currentTime else null,
                lastUpdated = currentTime,
                deviceId = deviceId
            )
        }

        /**
         * Creates a copy of existing DeviceInfo with updated fields
         */
        fun updateFrom(
            existing: DeviceInfo,
            deviceModel: String? = null,
            deviceBrand: String? = null,
            deviceName: String? = null,
            softwareId: String? = null,
            androidVersion: String? = null,
            apiLevel: Int? = null,
            fcmToken: String? = null,
            appVersion: String? = null
        ): DeviceInfo {
            return existing.copy(
                deviceModel = deviceModel ?: existing.deviceModel,
                deviceBrand = deviceBrand ?: existing.deviceBrand,
                deviceName = deviceName ?: existing.deviceName,
                softwareId = softwareId ?: existing.softwareId,
                androidVersion = androidVersion ?: existing.androidVersion,
                apiLevel = apiLevel ?: existing.apiLevel,
                fcmToken = fcmToken ?: existing.fcmToken,
                appVersion = appVersion ?: existing.appVersion,
                lastUpdated = Timestamp.now()
            )
        }

        /**
         * Compares two DeviceInfo objects to detect changes
         * Returns true if any field (except timestamps) has changed
         */
        fun hasChanged(current: DeviceInfo, new: DeviceInfo): Boolean {
            return current.deviceModel != new.deviceModel ||
                   current.deviceBrand != new.deviceBrand ||
                   current.deviceName != new.deviceName ||
                   current.softwareId != new.softwareId ||
                   current.androidVersion != new.androidVersion ||
                   current.apiLevel != new.apiLevel ||
                   current.fcmToken != new.fcmToken ||
                   current.appVersion != new.appVersion ||
                   current.deviceId != new.deviceId
        }
    }
}
