package com.ab.notif.utils

import android.content.Context
import android.content.SharedPreferences
import android.os.Build
import android.util.Log

/**
 * Utility class to generate and manage unique device ID for Firebase Firestore.
 * Device ID format: Brand-Model-AndroidVersion
 * Example: Samsung-Galaxy-S21-Android13, Xiaomi-Redmi-Note-10-Android11
 */
object DeviceIdManager {
    
    private const val TAG = "DEVICE_ID_MANAGER"
    private const val LOG_PREFIX = "[DEVICE_ID]"
    private const val PREFS_NAME = "notif_sync_prefs"
    private const val KEY_DEVICE_ID = "device_id"
    
    private var cachedDeviceId: String? = null
    
    /**
     * Gets the device ID, generating it if it doesn't exist
     */
    fun getDeviceId(context: Context): String {
        // Return cached value if available
        cachedDeviceId?.let { return it }
        
        val prefs = getSharedPreferences(context)
        val existingId = prefs.getString(KEY_DEVICE_ID, null)
        
        return if (existingId != null) {
            Log.d(TAG, "$LOG_PREFIX Retrieved existing device ID: $existingId")
            cachedDeviceId = existingId
            existingId
        } else {
            val newId = generateDeviceId()
            prefs.edit().putString(KEY_DEVICE_ID, newId).apply()
            cachedDeviceId = newId
            Log.i(TAG, "$LOG_PREFIX Generated new device ID: $newId")
            newId
        }
    }
    
    /**
     * Generates a unique device ID based on device information
     * Format: Brand-Model-AndroidVersion
     */
    private fun generateDeviceId(): String {
        val brand = sanitizeString(Build.BRAND)
        val model = sanitizeString(Build.MODEL)
        val androidVersion = "Android${Build.VERSION.SDK_INT}"
        
        val deviceId = "$brand-$model-$androidVersion"
        
        Log.d(TAG, "$LOG_PREFIX Device info - Brand: ${Build.BRAND}, Model: ${Build.MODEL}, SDK: ${Build.VERSION.SDK_INT}")
        Log.d(TAG, "$LOG_PREFIX Generated device ID: $deviceId")
        
        return deviceId
    }
    
    /**
     * Sanitizes a string to be safe for use in device ID
     * Removes special characters and spaces, capitalizes first letter
     */
    private fun sanitizeString(input: String): String {
        return input
            .replace(Regex("[^a-zA-Z0-9]"), "") // Remove special characters and spaces
            .take(20) // Limit length
            .lowercase()
            .replaceFirstChar { it.uppercase() } // Capitalize first letter
            .ifEmpty { "Unknown" } // Fallback if empty
    }
    
    /**
     * Gets SharedPreferences instance
     */
    private fun getSharedPreferences(context: Context): SharedPreferences {
        return context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }
    
    /**
     * Clears the stored device ID (for testing purposes)
     */
    fun clearDeviceId(context: Context) {
        getSharedPreferences(context).edit().remove(KEY_DEVICE_ID).apply()
        cachedDeviceId = null
        Log.i(TAG, "$LOG_PREFIX Device ID cleared")
    }
}
