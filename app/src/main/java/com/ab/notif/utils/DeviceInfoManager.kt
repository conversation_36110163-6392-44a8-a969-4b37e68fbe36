package com.ab.notif.utils

import android.content.Context
import android.os.Build
import android.provider.Settings
import android.util.Log
import com.ab.notif.BuildConfig
import com.ab.notif.data.DeviceInfo
import com.google.firebase.messaging.FirebaseMessaging
import kotlinx.coroutines.tasks.await

/**
 * Utility class for collecting and managing comprehensive device information.
 * Handles device info collection, comparison, and change detection for Firebase storage.
 * Phase 6: Device information storage and management.
 */
object DeviceInfoManager {

    private const val TAG = "DEVICE_INFO_MANAGER"
    private const val LOG_PREFIX = "[DEVICE-INFO]"

    /**
     * Collects comprehensive device information including FCM token
     */
    suspend fun collectDeviceInfo(context: Context): DeviceInfo? {
        return try {
            Log.d(TAG, "$LOG_PREFIX Collecting device information...")

            // Collect basic device information
            val deviceModel = Build.MODEL
            val deviceBrand = Build.MANUFACTURER
            val deviceName = Build.DEVICE
            val androidVersion = Build.VERSION.RELEASE
            val apiLevel = Build.VERSION.SDK_INT
            val appVersion = BuildConfig.VERSION_NAME

            // Get software ID (Android ID)
            val softwareId = getSoftwareId(context)

            // Get current FCM token
            val fcmToken = getFCMToken()

            // Get device ID from existing manager
            val deviceId = DeviceIdManager.getDeviceId(context)

            Log.d(TAG, "$LOG_PREFIX Device info collected - Model: $deviceModel, Brand: $deviceBrand, Android: $androidVersion")

            DeviceInfo.create(
                deviceModel = deviceModel,
                deviceBrand = deviceBrand,
                deviceName = deviceName,
                softwareId = softwareId,
                androidVersion = androidVersion,
                apiLevel = apiLevel,
                fcmToken = fcmToken,
                appVersion = appVersion,
                deviceId = deviceId,
                isFirstTime = true // Will be adjusted when storing
            )

        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX Failed to collect device information: ${e.message}", e)
            null
        }
    }

    /**
     * Gets the Android ID as software identifier
     */
    private fun getSoftwareId(context: Context): String {
        return try {
            Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID) ?: "unknown"
        } catch (e: Exception) {
            Log.w(TAG, "$LOG_PREFIX Failed to get Android ID: ${e.message}")
            "unknown"
        }
    }

    /**
     * Gets the current FCM token
     */
    private suspend fun getFCMToken(): String {
        return try {
            FirebaseMessaging.getInstance().token.await()
        } catch (e: Exception) {
            Log.w(TAG, "$LOG_PREFIX Failed to get FCM token: ${e.message}")
            ""
        }
    }

    /**
     * Creates device info for FCM token update only
     */
    suspend fun createTokenUpdateInfo(context: Context, newToken: String): DeviceInfo? {
        return try {
            val deviceId = DeviceIdManager.getDeviceId(context)

            DeviceInfo(
                fcmToken = newToken,
                deviceId = deviceId
            )
        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX Failed to create token update info: ${e.message}", e)
            null
        }
    }

    /**
     * Compares current device info with stored info to detect changes
     */
    suspend fun detectChanges(context: Context, storedInfo: DeviceInfo?): DeviceInfo? {
        if (storedInfo == null) {
            Log.d(TAG, "$LOG_PREFIX No stored device info found, collecting fresh info")
            return collectDeviceInfo(context)
        }

        val currentInfo = collectDeviceInfo(context) ?: return null

        // Compare with stored info (excluding timestamps)
        val hasChanges = DeviceInfo.hasChanged(storedInfo, currentInfo)

        if (hasChanges) {
            Log.i(TAG, "$LOG_PREFIX Device info changes detected")
            return DeviceInfo.updateFrom(
                existing = storedInfo,
                deviceModel = currentInfo.deviceModel,
                deviceBrand = currentInfo.deviceBrand,
                deviceName = currentInfo.deviceName,
                softwareId = currentInfo.softwareId,
                androidVersion = currentInfo.androidVersion,
                apiLevel = currentInfo.apiLevel,
                fcmToken = currentInfo.fcmToken,
                appVersion = currentInfo.appVersion
            )
        } else {
            Log.d(TAG, "$LOG_PREFIX No device info changes detected")
            return null
        }
    }

    /**
     * Logs device information for debugging
     */
    fun logDeviceInfo(deviceInfo: DeviceInfo) {
        Log.d(TAG, "$LOG_PREFIX Device Information:")
        Log.d(TAG, "$LOG_PREFIX - Model: ${deviceInfo.deviceModel}")
        Log.d(TAG, "$LOG_PREFIX - Brand: ${deviceInfo.deviceBrand}")
        Log.d(TAG, "$LOG_PREFIX - Name: ${deviceInfo.deviceName}")
        Log.d(TAG, "$LOG_PREFIX - Software ID: ${deviceInfo.softwareId}")
        Log.d(TAG, "$LOG_PREFIX - Android Version: ${deviceInfo.androidVersion}")
        Log.d(TAG, "$LOG_PREFIX - API Level: ${deviceInfo.apiLevel}")
        Log.d(TAG, "$LOG_PREFIX - App Version: ${deviceInfo.appVersion}")
        Log.d(TAG, "$LOG_PREFIX - FCM Token: ${deviceInfo.fcmToken.take(20)}...")
        Log.d(TAG, "$LOG_PREFIX - Device ID: ${deviceInfo.deviceId}")
    }
}
