package com.ab.notif.utils

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.location.Location
import android.util.Log
import androidx.core.app.ActivityCompat
import com.ab.notif.data.LocationData
import com.ab.notif.firebase.FirebaseManager
import com.ab.notif.firebase.SyncResult
import com.google.android.gms.location.*
import com.google.android.gms.tasks.CancellationTokenSource
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withTimeoutOrNull
import java.text.SimpleDateFormat
import java.util.*
import kotlin.coroutines.resume

/**
 * Utility class for instant high-accuracy location capture triggered by FCM messages.
 * This bypasses the normal WorkManager schedule and provides immediate location results.
 * Phase 5: FCM instant location capture utility.
 */
class InstantLocationCapture private constructor() {

    companion object {
        private const val TAG = "INSTANT_LOCATION"
        private const val LOG_PREFIX = "[FCM-LOCATION]"
        private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        
        // High-accuracy location request settings
        private const val LOCATION_TIMEOUT_MS = 30000L // 30 seconds timeout
        private const val HIGH_ACCURACY_INTERVAL_MS = 1000L // 1 second
        private const val HIGH_ACCURACY_FASTEST_INTERVAL_MS = 500L // 0.5 seconds
        
        @Volatile
        private var INSTANCE: InstantLocationCapture? = null
        
        fun getInstance(): InstantLocationCapture {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: InstantLocationCapture().also { INSTANCE = it }
            }
        }
    }

    private val firebaseManager = FirebaseManager.getInstance()

    /**
     * Captures location immediately with high accuracy and syncs to Firebase.
     * This method is designed to be called from FCM message handling.
     */
    suspend fun captureAndSyncLocation(context: Context): Boolean {
        val currentTime = dateFormat.format(Date())
        Log.d(TAG, "$LOG_PREFIX $currentTime | Starting instant location capture")

        return try {
            if (!hasLocationPermissions(context)) {
                Log.w(TAG, "$LOG_PREFIX $currentTime | Location permissions not granted")
                return false
            }

            val location = getHighAccuracyLocation(context)
            if (location != null) {
                logAndSyncLocation(context, location)
                Log.i(TAG, "$LOG_PREFIX $currentTime | Instant location capture completed successfully")
                true
            } else {
                Log.w(TAG, "$LOG_PREFIX $currentTime | Failed to get instant location")
                false
            }

        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX $currentTime | Error in instant location capture: ${e.message}", e)
            false
        }
    }

    /**
     * Gets high-accuracy location with timeout
     */
    private suspend fun getHighAccuracyLocation(context: Context): Location? {
        return withTimeoutOrNull(LOCATION_TIMEOUT_MS) {
            suspendCancellableCoroutine { continuation ->
                val fusedLocationClient = LocationServices.getFusedLocationProviderClient(context)
                val cancellationTokenSource = CancellationTokenSource()

                // Configure high-accuracy location request
                val locationRequest = LocationRequest.Builder(Priority.PRIORITY_HIGH_ACCURACY, HIGH_ACCURACY_INTERVAL_MS)
                    .setMinUpdateIntervalMillis(HIGH_ACCURACY_FASTEST_INTERVAL_MS)
                    .setMaxUpdates(1) // Only need one location
                    .build()

                try {
                    // First try to get last known location for quick response
                    if (ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
                        fusedLocationClient.lastLocation.addOnSuccessListener { lastLocation ->
                            if (lastLocation != null && isLocationRecent(lastLocation)) {
                                Log.d(TAG, "$LOG_PREFIX Using recent cached location")
                                if (continuation.isActive) {
                                    continuation.resume(lastLocation)
                                }
                                return@addOnSuccessListener
                            }

                            // If no recent cached location, request fresh location
                            val locationCallback = object : LocationCallback() {
                                override fun onLocationResult(locationResult: LocationResult) {
                                    val location = locationResult.lastLocation
                                    if (location != null && continuation.isActive) {
                                        fusedLocationClient.removeLocationUpdates(this)
                                        continuation.resume(location)
                                    }
                                }
                            }

                            // Request fresh location updates
                            fusedLocationClient.requestLocationUpdates(
                                locationRequest,
                                locationCallback,
                                null
                            )

                            // Set up cancellation
                            continuation.invokeOnCancellation {
                                fusedLocationClient.removeLocationUpdates(locationCallback)
                                cancellationTokenSource.cancel()
                            }
                        }
                    }
                } catch (e: SecurityException) {
                    Log.e(TAG, "$LOG_PREFIX Security exception getting location: ${e.message}")
                    if (continuation.isActive) {
                        continuation.resume(null)
                    }
                }
            }
        }
    }

    /**
     * Checks if location is recent (within last 5 minutes)
     */
    private fun isLocationRecent(location: Location): Boolean {
        val locationAge = System.currentTimeMillis() - location.time
        return locationAge < 5 * 60 * 1000 // 5 minutes
    }

    /**
     * Logs and syncs location to Firebase with enhanced logging format
     */
    private suspend fun logAndSyncLocation(context: Context, location: Location) {
        val currentTime = dateFormat.format(Date())
        val latitude = String.format("%.4f", location.latitude)
        val longitude = String.format("%.4f", location.longitude)
        val accuracy = String.format("%.1f", location.accuracy)

        // Base log message (Phase 1 & 2 format preserved)
        val baseLogMessage = "$LOG_PREFIX $currentTime | Instant location triggered | Lat: $latitude, Lng: $longitude | Accuracy: ${accuracy}m"

        try {
            // Create location data for Firebase
            val locationData = LocationData.create(location, "")  // Device ID will be set by FirebaseManager

            // Sync to Firebase
            val syncResult = firebaseManager.syncLocation(context, locationData)

            // Enhanced logging with sync status
            val syncStatus = when (syncResult) {
                SyncResult.SYNCED -> "SYNCED"
                SyncResult.FAILED -> "SYNC_FAILED"
            }

            val enhancedLogMessage = "$baseLogMessage | $syncStatus"
            Log.i(TAG, enhancedLogMessage)

        } catch (e: Exception) {
            // If Firebase sync fails, still log the location
            Log.i(TAG, "$baseLogMessage | SYNC_ERROR")
            Log.e(TAG, "$LOG_PREFIX Firebase sync error: ${e.message}", e)
        }
    }

    /**
     * Checks if location permissions are granted
     */
    private fun hasLocationPermissions(context: Context): Boolean {
        return ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED &&
                ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_COARSE_LOCATION) == PackageManager.PERMISSION_GRANTED
    }
}
