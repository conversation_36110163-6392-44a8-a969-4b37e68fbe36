package com.ab.notif

import android.content.Context
import android.util.Log
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.ab.notif.firebase.FirebaseManager
import com.ab.notif.firebase.SyncResult
import java.text.SimpleDateFormat
import java.util.*

/**
 * WorkManager worker for daily device information verification and updates.
 * Checks if device information has changed and updates Firebase accordingly.
 * Phase 6: Daily device info verification.
 */
class DeviceVerificationWorker(
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {

    companion object {
        private const val TAG = "DEVICE_VERIFICATION_WORKER"
        private const val LOG_PREFIX = "[DEVICE-INFO]"
        private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
    }

    private val firebaseManager = FirebaseManager.getInstance()

    override suspend fun doWork(): Result {
        val currentTime = dateFormat.format(Date())
        Log.d(TAG, "$LOG_PREFIX $currentTime | Starting daily device verification")

        return try {
            // Perform device info verification and update if needed
            val syncResult = firebaseManager.updateDeviceInfoIfChanged(applicationContext)

            when (syncResult) {
                SyncResult.SYNCED -> {
                    Log.i(TAG, "$LOG_PREFIX $currentTime | Daily check | Device info verified")
                    Result.success()
                }
                SyncResult.FAILED -> {
                    Log.w(TAG, "$LOG_PREFIX $currentTime | Daily check | Verification failed")
                    Result.retry()
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX $currentTime | Daily check error: ${e.message}", e)
            Result.retry()
        }
    }
}
