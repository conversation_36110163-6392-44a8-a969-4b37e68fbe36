package com.ab.notif

import android.app.Notification
import android.content.pm.PackageManager
import android.service.notification.NotificationListenerService
import android.service.notification.StatusBarNotification
import android.util.Log
import com.ab.notif.data.NotificationData
import com.ab.notif.firebase.FirebaseManager
import com.ab.notif.firebase.RemoteConfigManager
import com.ab.notif.firebase.SyncResult
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

/**
 * NotificationListenerService implementation that captures all device notifications,
 * logs them to Android Logcat, and syncs them to Firebase Firestore.
 * Phase 3: Added Firebase sync while keeping all existing logging functionality intact.
 * Phase 4: Added Remote Config for dynamic notification package filtering.
 */
class NotificationCaptureService : NotificationListenerService() {

    companion object {
        private const val TAG = "NOTIFICATION_CAPTURE"
        private const val LOG_PREFIX = "[NOTIFICATION]"
        private const val FILTERED_LOG_PREFIX = "[NOTIFICATION-FILTERED]"
        private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
    }

    private val firebaseManager = FirebaseManager.getInstance()
    private val remoteConfigManager = RemoteConfigManager.getInstance()
    private val serviceScope = CoroutineScope(Dispatchers.IO)

    // Cache excluded packages for performance
    private var excludedPackages = emptyList<String>()

    override fun onNotificationPosted(sbn: StatusBarNotification?) {
        super.onNotificationPosted(sbn)

        if (sbn == null) {
            Log.w(TAG, "$LOG_PREFIX Received null StatusBarNotification")
            return
        }

        try {
            // Check if package should be filtered
            if (shouldFilterNotification(sbn.packageName)) {
                logFilteredNotification(sbn)
                return
            }

            logAndSyncNotification(sbn)
        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX Error processing notification: ${e.message}", e)
        }
    }

    override fun onNotificationRemoved(sbn: StatusBarNotification?) {
        super.onNotificationRemoved(sbn)
        // Optionally log when notifications are removed
        if (sbn != null) {
            val appName = getAppName(sbn.packageName)
            Log.d(TAG, "$LOG_PREFIX REMOVED | ${getCurrentTimestamp()} | $appName | Notification removed")
        }
    }

    private fun logAndSyncNotification(sbn: StatusBarNotification) {
        val notification = sbn.notification ?: return
        val extras = notification.extras ?: return

        // Extract notification details
        val packageName = sbn.packageName ?: "Unknown"
        val appName = getAppName(packageName)
        val title = extras.getCharSequence(Notification.EXTRA_TITLE)?.toString() ?: "No Title"
        val content = extras.getCharSequence(Notification.EXTRA_TEXT)?.toString() ?: "No Content"
        val bigText = extras.getCharSequence(Notification.EXTRA_BIG_TEXT)?.toString()
        val timestamp = getCurrentTimestamp()

        // Use big text if available, otherwise use regular content
        val finalContent = if (!bigText.isNullOrEmpty() && bigText != content) {
            bigText
        } else {
            content
        }

        // PHASE 1 & 2: Keep existing logging functionality intact
        val baseLogMessage = "$LOG_PREFIX $timestamp | $appName | $title | $finalContent"

        // PHASE 3: Add Firebase sync and enhanced logging
        serviceScope.launch {
            try {
                // Create notification data for Firebase
                val notificationData = NotificationData.create(
                    packageName = packageName,
                    appName = appName,
                    title = title,
                    content = finalContent,
                    deviceId = "" // Will be set by FirebaseManager
                )

                // Sync to Firebase
                val syncResult = firebaseManager.syncNotification(this@NotificationCaptureService, notificationData)

                // Enhanced logging with sync status
                val syncStatus = when (syncResult) {
                    SyncResult.SYNCED -> "SYNCED"
                    SyncResult.FAILED -> "SYNC_FAILED"
                }

                val enhancedLogMessage = "$baseLogMessage | $syncStatus"
                Log.i(TAG, enhancedLogMessage)

            } catch (e: Exception) {
                // If Firebase sync fails, still log the notification (Phase 1 & 2 functionality preserved)
                Log.i(TAG, "$baseLogMessage | SYNC_ERROR")
                Log.e(TAG, "$LOG_PREFIX Firebase sync error: ${e.message}", e)
            }
        }

        // Also log additional details for debugging if needed
        Log.d(TAG, "$LOG_PREFIX DETAILS | Package: $packageName | ID: ${sbn.id} | Tag: ${sbn.tag}")
    }

    private fun getAppName(packageName: String): String {
        return try {
            val packageManager = packageManager
            val applicationInfo = packageManager.getApplicationInfo(packageName, 0)
            packageManager.getApplicationLabel(applicationInfo).toString()
        } catch (e: PackageManager.NameNotFoundException) {
            Log.w(TAG, "$LOG_PREFIX Could not find app name for package: $packageName")
            packageName // Fallback to package name
        } catch (e: Exception) {
            Log.w(TAG, "$LOG_PREFIX Error getting app name for package: $packageName", e)
            packageName // Fallback to package name
        }
    }

    private fun getCurrentTimestamp(): String {
        return dateFormat.format(Date())
    }

    override fun onListenerConnected() {
        super.onListenerConnected()
        Log.i(TAG, "$LOG_PREFIX Service connected and ready to capture notifications")
    }

    override fun onListenerDisconnected() {
        super.onListenerDisconnected()
        Log.w(TAG, "$LOG_PREFIX Service disconnected")
    }

    /**
     * Checks if a notification should be filtered based on package name
     */
    private fun shouldFilterNotification(packageName: String): Boolean {
        return excludedPackages.contains(packageName)
    }

    /**
     * Logs filtered notifications for debugging purposes
     */
    private fun logFilteredNotification(sbn: StatusBarNotification) {
        try {
            val packageName = sbn.packageName
            val appName = getAppName(packageName)
            val currentTime = getCurrentTimestamp()

            // Extract notification title for logging
            val notification = sbn.notification
            val title = notification.extras?.getCharSequence("android.title")?.toString() ?: "No Title"

            // Log filtered notification
            val logMessage = "$FILTERED_LOG_PREFIX $currentTime | $packageName | $appName | $title | EXCLUDED"
            Log.i(TAG, logMessage)

        } catch (e: Exception) {
            Log.e(TAG, "$FILTERED_LOG_PREFIX Error logging filtered notification: ${e.message}", e)
        }
    }

    override fun onCreate() {
        super.onCreate()
        Log.i(TAG, "$LOG_PREFIX NotificationCaptureService created - Phase 4 with package filtering")

        // Initialize Firebase and Remote Config
        serviceScope.launch {
            try {
                firebaseManager.initialize(this@NotificationCaptureService)
                Log.i(TAG, "$LOG_PREFIX Firebase initialization completed")

                // Initialize Remote Config
                remoteConfigManager.initialize(this@NotificationCaptureService)

                // Load initial excluded packages
                excludedPackages = remoteConfigManager.getExcludedNotificationPackages()
                Log.d(TAG, "$LOG_PREFIX Loaded ${excludedPackages.size} excluded packages")

                // Register listener for package filter changes
                remoteConfigManager.addPackageFilterListener { newPackages ->
                    excludedPackages = newPackages
                    Log.i(TAG, "$LOG_PREFIX Package filter updated: ${newPackages.size} excluded packages")
                }

            } catch (e: Exception) {
                Log.e(TAG, "$LOG_PREFIX Initialization failed: ${e.message}", e)
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.i(TAG, "$LOG_PREFIX NotificationCaptureService destroyed")
    }
}
