**Task: Android App Phase 4 - Add Firebase Remote Config for Dynamic Settings**

**Objective:**
Extend the existing app to use Firebase Remote Config for dynamic configuration of location intervals and notification filtering. Keep all existing functionality intact.

**Requirements:**
1. **Keep ALL existing functionality** - notification capture, location tracking, Firebase sync, and logging
2. **Add Firebase Remote Config** to control app behavior remotely
3. **Dynamic location interval** - configurable from Firebase console
4. **Notification filtering** - exclude specific package names from being captured/synced
5. **Real-time config updates** - app should check for config changes periodically

**Firebase Remote Config Parameters:**
```json
{
  "location_interval_minutes": 15,
  "excluded_notification_packages": [
    "com.android.systemui",
    "com.example.sensitiveapp"
  ]
}
```

**Technical Specifications:**
- Add Firebase Remote Config SDK
- Fetch config on app start and every 12 hours
- Apply location interval changes to WorkManager dynamically
- Filter notifications based on package name list before logging/syncing
- Use default values when config fetch fails

**Config Parameter Details:**

1. **`location_interval_minutes`** (Long, default: 15)
   - Controls how often location is captured
   - Minimum: 5 minutes, Maximum: 120 minutes
   - Updates WorkManager periodic request when changed

2. **`excluded_notification_packages`** (String Array, default: empty)
   - List of package names to exclude from notification capture
   - Format: JSON array of strings
   - Example: `["com.android.systemui", "com.banking.app", "com.privacy.messenger"]`

**Implementation Requirements:**
1. Config fetch and cache mechanism
2. Dynamic WorkManager interval updates
3. Package name filtering in NotificationListenerService
4. Fallback to default values on config fetch failure
5. Config refresh every 12 hours

**Enhanced Logging Format:**
```
[CONFIG] 2024-01-15 14:00:00 | Location interval updated: 20 minutes
[CONFIG] 2024-01-15 14:00:01 | Excluded packages: com.android.systemui, com.banking.app
[NOTIFICATION] 2024-01-15 14:30:25 | WhatsApp | John Doe | New message received | SYNCED
[NOTIFICATION-FILTERED] 2024-01-15 14:30:30 | com.android.systemui | System UI | EXCLUDED
[LOCATION] 2024-01-15 14:50:30 | Lat: -6.2088, Lng: 106.8456 | Accuracy: 12.5m | SYNCED
```

**Additional Components Needed:**
1. Firebase Remote Config initialization
2. Config fetch and apply logic
3. WorkManager interval update mechanism
4. Notification package filtering
5. Config refresh scheduler

**Important Notes:**
- Don't break any existing functionality
- Handle config fetch failures gracefully (use defaults)
- Log all config changes for debugging
- Update WorkManager constraints when interval changes
- Apply package filtering immediately when config updates

**Deliverables:**
1. Updated Android Studio project with Remote Config
2. Dynamic location interval control from Firebase console
3. Notification filtering based on package names
4. Config fetch and refresh mechanism
5. Enhanced logging showing config changes and filtering actions
6. Firebase Remote Config setup instructions
