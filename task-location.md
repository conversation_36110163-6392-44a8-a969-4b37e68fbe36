**Task: Android App Phase 2 - Add Location Capture with Logging**

**Objective:**
Extend the existing Phase 1 app to also capture device location every 15 minutes and log it. Keep the notification capture functionality from Phase 1 intact.

**Requirements:**
1. **Keep existing notification capture** from Phase 1 working
2. **Add location tracking** - fetch GPS location every 15 minutes
3. **Log location data** to Android Logcat with clear, readable format
4. **Background execution** - location should be captured even when app is not active
5. **No UI, no Firebase, no database** - just logging

**Technical Specifications:**
- Build upon Phase 1 code
- Use FusedLocationProviderClient for location accuracy
- Use WorkManager for reliable 15-minute interval location updates
- Handle location permission states gracefully
- Continue logging notifications as before

**Additional Components Needed:**
1. WorkManager periodic work request (15-minute intervals)
2. Location worker class using FusedLocationProviderClient
3. Location permission handling
4. Foreground service for background location access

**Additional Permissions Required:**
- ACCESS_FINE_LOCATION
- ACCESS_BACKGROUND_LOCATION
- FOREGROUND_SERVICE
- FOREGROUND_SERVICE_LOCATION

**Logging Format Example:**
```
[NOTIFICATION] 2024-01-15 14:30:25 | WhatsApp | John Doe | New message received
[LOCATION] 2024-01-15 14:30:30 | Lat: -6.2088, Lng: 106.8456 | Accuracy: 12.5m
[LOCATION] 2024-01-15 14:45:30 | Lat: -6.2090, Lng: 106.8460 | Accuracy: 8.2m
```

**Important Notes:**
- Don't break existing notification functionality
- Handle location permission denied gracefully (log error, continue running)
- Use battery-efficient location settings
- Account for Android background location restrictions
- Keep code simple - no complex location caching or filtering

**Deliverables:**
1. Updated Android Studio project with both notification + location capture
2. Working location tracking every 15 minutes
3. Updated AndroidManifest.xml with location permissions
4. Instructions for enabling location permissions (including background location)
