# Firebase Setup Instructions - Phase 3

## Overview
Phase 3 of NotifSync Lite requires Firebase Firestore to sync notification and location data to the cloud. This guide will walk you through setting up a Firebase project and configuring the app.

## Prerequisites
- Google account
- Android Studio with the NotifSync Lite project
- Internet connection

## Step 1: Create Firebase Project

1. **Go to Firebase Console**
   - Visit [https://console.firebase.google.com/](https://console.firebase.google.com/)
   - Sign in with your Google account

2. **Create New Project**
   - Click "Create a project" or "Add project"
   - Enter project name: `NotifSync-Lite` (or your preferred name)
   - Click "Continue"

3. **Configure Google Analytics** (Optional)
   - Choose whether to enable Google Analytics
   - If enabled, select or create an Analytics account
   - Click "Create project"

4. **Wait for Project Creation**
   - Firebase will set up your project (takes 1-2 minutes)
   - Click "Continue" when ready

## Step 2: Add Android App to Firebase

1. **Add Android App**
   - In the Firebase console, click the Android icon to add an Android app
   - Or go to Project Settings > General > Your apps > Add app

2. **Register App**
   - **Android package name**: `com.ab.notif` (must match exactly)
   - **App nickname**: `NotifSync Lite` (optional)
   - **Debug signing certificate SHA-1**: Leave blank for now (optional)
   - Click "Register app"

3. **Download google-services.json**
   - Click "Download google-services.json"
   - **IMPORTANT**: Save this file - you'll need it in the next step
   - Click "Next"

4. **Skip SDK Setup**
   - The project already has Firebase SDK configured
   - Click "Next" and "Continue to console"

## Step 3: Add google-services.json to Project

1. **Locate the File**
   - Find the `google-services.json` file you downloaded
   - This file contains your Firebase project configuration

2. **Add to Android Project**
   - In Android Studio, switch to "Project" view (not "Android" view)
   - Navigate to `app/` folder (same level as `app/build.gradle.kts`)
   - Copy `google-services.json` into the `app/` folder
   - The file should be at: `NotifSyncLite/app/google-services.json`

3. **Verify Placement**
   - The file structure should look like:
   ```
   NotifSyncLite/
   ├── app/
   │   ├── google-services.json  ← HERE
   │   ├── build.gradle.kts
   │   └── src/
   ├── build.gradle.kts
   └── settings.gradle.kts
   ```

## Step 4: Configure Firestore Database

1. **Enable Firestore**
   - In Firebase console, go to "Firestore Database"
   - Click "Create database"

2. **Choose Security Rules**
   - Select "Start in test mode" (allows read/write for 30 days)
   - **Note**: For production, you'll need proper security rules
   - Click "Next"

3. **Select Location**
   - Choose a location close to your users
   - Click "Done"

4. **Wait for Database Creation**
   - Firestore will initialize (takes 1-2 minutes)

## Step 5: Configure Authentication

1. **Enable Authentication**
   - In Firebase console, go to "Authentication"
   - Click "Get started"

2. **Enable Anonymous Authentication**
   - Go to "Sign-in method" tab
   - Click on "Anonymous"
   - Toggle "Enable" to ON
   - Click "Save"

## Step 6: Build and Test

1. **Clean and Rebuild**
   ```bash
   ./gradlew clean
   ./gradlew build
   ```

2. **Install on Device**
   ```bash
   ./gradlew assembleDebug
   adb install app/build/outputs/apk/debug/app-debug.apk
   ```

3. **Check Logs**
   ```bash
   adb logcat -s NOTIFICATION_CAPTURE -s LOCATION_CAPTURE -s FIREBASE_MANAGER
   ```

4. **Look for Firebase Logs**
   - You should see logs like:
   ```
   [FIREBASE] Firebase initialized successfully
   [NOTIFICATION] 2024-01-15 14:30:25 | WhatsApp | John Doe | New message | SYNCED
   [LOCATION] 2024-01-15 14:30:30 | Lat: -6.2088, Lng: 106.8456 | Accuracy: 12.5m | SYNCED
   ```

## Step 7: Verify Data in Firebase Console

1. **Check Firestore Data**
   - Go to Firebase console > Firestore Database
   - You should see collections structure:
   ```
   devices/
   └── [DEVICE_ID]/
       ├── notifications/
       │   └── [auto-generated-docs]
       └── locations/
           └── [auto-generated-docs]
   ```

2. **Monitor Real-time Updates**
   - Data should appear in real-time as notifications and locations are captured
   - Each document will have the fields specified in the requirements

## Troubleshooting

### Build Errors
- **"google-services.json not found"**: Ensure file is in `app/` folder, not `app/src/`
- **"Package name mismatch"**: Verify package name in Firebase matches `com.ab.notif`

### Firebase Connection Issues
- **"Firebase not initialized"**: Check internet connection and google-services.json
- **"Authentication failed"**: Ensure Anonymous auth is enabled in Firebase console

### Sync Failures
- **"SYNC_FAILED" in logs**: Check Firestore rules and internet connectivity
- **No data in Firestore**: Verify app permissions and Firebase configuration

### Security Rules (For Production)
Replace test mode rules with:
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /devices/{deviceId}/{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

## Important Notes

- **google-services.json is sensitive**: Don't commit to public repositories
- **Test mode expires**: Update Firestore rules before 30 days
- **Offline support**: Firebase automatically handles offline sync
- **Data structure**: Follows the exact format specified in Phase 3 requirements

## Support

If you encounter issues:
1. Check the troubleshooting section above
2. Verify all steps were followed correctly
3. Check Firebase console for error messages
4. Review app logs for detailed error information
