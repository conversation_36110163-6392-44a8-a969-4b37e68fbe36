
**Task: Android App Phase 5 - Add Firebase Cloud Messaging for Instant Location Request**

**Objective:**
Extend the existing app to receive Firebase Cloud Messages (FCM) that trigger immediate location capture and sync. The app should respond to remote location requests without showing any notifications to the user.

**Requirements:**
1. **Keep ALL existing functionality** - notification capture, location tracking, Firebase sync, Remote Config, and logging
2. **Add FCM integration** for receiving remote commands
3. **Silent notification handling** - no visible notifications shown to user
4. **Instant location capture** - immediately fetch and sync location when FCM message received
5. **Command-based messaging** - support different message types for future expansion

**FCM Message Structure:**
```json
{
  "data": {
    "command": "get_location",
    "timestamp": "1642251600000"
  }
}
```

**Technical Specifications:**
- Add Firebase Cloud Messaging SDK
- Implement FirebaseMessagingService for background message handling
- Handle messages when app is in background/foreground/killed
- Trigger immediate location fetch on "get_location" command
- Log FCM message receipt and processing

**Message Commands:**

1. **`get_location`** - Immediately capture and sync current location
   - Bypass normal 15-minute interval
   - Use high-accuracy location request
   - Sync to Firebase immediately after capture

**Implementation Requirements:**
1. FCM service registration and token handling
2. Background message processing
3. Immediate location fetch mechanism (separate from WorkManager)
4. High-priority location request for instant results
5. Message command parsing and validation

**Enhanced Logging Format:**
```
[FCM] 2024-01-15 14:30:25 | Message received | Command: get_location
[FCM-LOCATION] 2024-01-15 14:30:30 | Instant location triggered | Lat: -6.2088, Lng: 106.8456 | Accuracy: 8.2m | SYNCED
[LOCATION] 2024-01-15 14:45:00 | Lat: -6.2090, Lng: 106.8460 | Accuracy: 12.5m | SYNCED (scheduled)
```

**Additional Components Needed:**
1. FirebaseMessagingService implementation
2. FCM token generation and logging
3. Instant location fetch utility (separate from WorkManager)
4. Message command processor
5. High-accuracy location request handler

**Important Notes:**
- Don't show any visible notifications to user
- Handle FCM messages even when app is killed
- Use high-accuracy location settings for instant requests
- Keep existing periodic location tracking unchanged
- Log FCM token for testing purposes
- Handle location permission issues gracefully for instant requests

**Firebase Console Setup:**
- The app should log its FCM registration token on startup for easy testing
- Support data-only messages (no notification payload)

**Testing Approach:**
- Log FCM registration token clearly for manual message sending
- Test instant location requests from Firebase Console
- Verify background message handling works

**Deliverables:**
1. Updated Android Studio project with FCM integration
2. Silent FCM message handling
3. Instant location capture on remote command
4. FCM token logging for testing
5. Enhanced logging showing FCM activity
6. FCM setup and testing instructions
