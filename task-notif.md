**Task: Android App Phase 1 - Notification Capture with Logging**

**Objective:**
Create a simple Android app that captures ALL device notifications and logs them. No UI, no Firebase, no database - just pure notification listening and logging.

**Requirements:**
1. **No UI** - completely headless app
2. **Capture ALL notifications** from any app on the device
3. **Log notification details** to Android Logcat with clear, readable format
4. **Auto-start** - begin capturing immediately after installation and device boot

**Technical Specifications:**
- Target Android API 31+
- Use NotificationListenerService to intercept notifications
- Log every notification with timestamp, app name, title, and content
- Handle service lifecycle properly (start/stop/restart)

**Key Components Needed:**
1. NotificationListenerService implementation
2. Boot receiver to auto-start the service
3. Proper AndroidManifest.xml configuration
4. Clear logging format for easy debugging

**Permissions Required:**
- BIND_NOTIFICATION_LISTENER_SERVICE
- RECEIVE_BOOT_COMPLETED

**Logging Format Example:**
```
[NOTIFICATION] 2024-01-15 14:30:25 | WhatsApp | <PERSON> | New message received
[NOTIFICATION] 2024-01-15 14:31:10 | Gmail | Google | You have 3 new emails
```

**Important Notes:**
- Keep code extremely simple - no complex architecture
- Focus on reliability of notification capture
- Add basic null-safety for notification fields
- Include setup instructions for enabling notification access permission
- No need for data persistence - just live logging

**Deliverables:**
1. Complete Android Studio project
2. Working NotificationListenerService
3. AndroidManifest.xml with proper service declaration
4. Instructions on how to enable notification access in device settings
