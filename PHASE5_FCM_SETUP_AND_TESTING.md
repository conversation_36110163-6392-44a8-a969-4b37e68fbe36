# Phase 5: Firebase Cloud Messaging Setup and Testing Guide

## Overview
Phase 5 adds Firebase Cloud Messaging (FCM) integration to enable instant location capture via remote commands. The app can now receive silent FCM messages and immediately capture and sync location data without showing any notifications to the user.

## New Components Added

### 1. FCMMessagingService
- **Location**: `app/src/main/java/com/ab/notif/FCMMessagingService.kt`
- **Purpose**: Handles incoming FCM messages and processes commands
- **Features**:
  - Silent message processing (no user notifications)
  - Command parsing and validation
  - Token refresh handling
  - Comprehensive logging

### 2. InstantLocationCapture Utility
- **Location**: `app/src/main/java/com/ab/notif/utils/InstantLocationCapture.kt`
- **Purpose**: Provides immediate high-accuracy location capture
- **Features**:
  - High-accuracy location requests
  - 30-second timeout for location capture
  - Uses cached location if recent (< 5 minutes)
  - Firebase sync with enhanced logging
  - Separate from WorkManager schedule

### 3. FCM Token Management
- **Location**: Updated `NotifSyncApplication.kt`
- **Purpose**: Initializes FCM and logs tokens for testing
- **Features**:
  - Automatic token generation on app start
  - Clear token logging for testing purposes
  - Token refresh handling

## FCM Message Format

### Supported Commands

#### get_location Command
```json
{
  "data": {
    "command": "get_location",
    "timestamp": "1642251600000"
  }
}
```

**Important**: Use data-only messages (no notification payload) to ensure silent processing.

## Testing Instructions

### Step 1: Get FCM Token
1. Install and run the app
2. Check Android Logcat for FCM token:
   ```
   [APPLICATION] 2024-01-15 14:30:00 | FCM Token for testing: [LONG_TOKEN_STRING]
   ```
3. Copy this token for testing

### Step 2: Send Test Message via Firebase Console
1. Go to Firebase Console → Cloud Messaging
2. Click "Send your first message"
3. **Important**: Skip the notification section entirely
4. Go directly to "Additional options"
5. Add custom data:
   - Key: `command`, Value: `get_location`
   - Key: `timestamp`, Value: `1642251600000` (optional)
6. In "Target" section, select "FCM registration token"
7. Paste the token from Step 1
8. Send the message

### Step 3: Verify Results
Check Android Logcat for the following sequence:

1. **FCM Message Receipt**:
   ```
   [FCM] 2024-01-15 14:30:25 | Message received | Command: get_location
   ```

2. **Instant Location Capture**:
   ```
   [FCM-LOCATION] 2024-01-15 14:30:30 | Instant location triggered | Lat: -6.2088, Lng: 106.8456 | Accuracy: 8.2m | SYNCED
   ```

3. **Regular Scheduled Location** (continues normally):
   ```
   [LOCATION] 2024-01-15 14:45:00 | Lat: -6.2090, Lng: 106.8460 | Accuracy: 12.5m | SYNCED (scheduled)
   ```

## Testing Scenarios

### 1. App in Foreground
- Send FCM message while app is visible
- Should process immediately
- No user notification shown

### 2. App in Background
- Send FCM message while app is in background
- Should wake up and process message
- No user notification shown

### 3. App Killed/Not Running
- Force stop the app
- Send FCM message
- App should start and process message
- No user notification shown

### 4. Location Permission Issues
- Revoke location permissions
- Send FCM message
- Should log permission error gracefully
- Should not crash

### 5. Network Issues
- Turn off internet
- Send FCM message (when internet returns)
- Should capture location but show SYNC_FAILED
- Should retry sync when network returns

## Troubleshooting

### FCM Token Not Appearing
- Check if google-services.json is properly configured
- Verify Firebase project setup
- Check for Firebase initialization errors in logs

### Messages Not Received
- Verify FCM token is correct and recent
- Ensure using data-only messages (no notification payload)
- Check if app has been force-stopped recently
- Verify Firebase project configuration

### Location Not Captured
- Check location permissions are granted
- Verify location services are enabled on device
- Check for location timeout errors in logs
- Ensure device has GPS/network location access

### Firebase Sync Issues
- Check internet connectivity
- Verify Firebase authentication is working
- Check Firestore rules allow writes
- Look for Firebase sync errors in logs

## Log Prefixes for Monitoring

- `[FCM]` - FCM service messages and token management
- `[FCM-LOCATION]` - Instant location capture triggered by FCM
- `[LOCATION]` - Regular scheduled location capture
- `[APPLICATION]` - App initialization and FCM setup

## Security Considerations

1. **No User Notifications**: FCM messages are processed silently
2. **Data-Only Messages**: Only data payloads are processed, no notification UI
3. **Command Validation**: Unknown commands are logged and ignored
4. **Permission Checks**: Location permissions are verified before capture
5. **Error Handling**: All errors are logged but don't crash the app

## Firebase Console Message Testing

### Correct Message Format (Data-Only)
```json
{
  "data": {
    "command": "get_location",
    "timestamp": "1642251600000"
  },
  "token": "YOUR_FCM_TOKEN_HERE"
}
```

### Incorrect Format (Will Show Notification)
```json
{
  "notification": {
    "title": "Location Request",
    "body": "Please share location"
  },
  "data": {
    "command": "get_location"
  },
  "token": "YOUR_FCM_TOKEN_HERE"
}
```

**Always use data-only messages for silent processing.**

## Integration with Existing Features

- **Notification Capture**: Continues working normally
- **Scheduled Location**: Continues every 15 minutes (or Remote Config interval)
- **Firebase Sync**: Both instant and scheduled locations sync to same Firestore collections
- **Remote Config**: All existing configuration continues to work
- **Boot Receiver**: FCM service starts automatically on device boot

## Next Steps

After successful testing, you can:
1. Integrate with a backend service to send FCM messages programmatically
2. Add more FCM commands for additional functionality
3. Implement FCM topic subscriptions for group messaging
4. Add FCM analytics and delivery tracking
